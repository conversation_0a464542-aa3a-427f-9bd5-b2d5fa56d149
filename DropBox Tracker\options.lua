return
{
    enable = true,
    customFoV1 = 87,
    customScreenResX = 2560,
    tracker1 = {
        boxOffsetX = 0,
        RareUnit = {
            borderSize = 3,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -52222,
            showBox = true,
        },
        Trimate = {
            showName = true,
            borderSize = 3,
            customBorderColor = -9778804,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        showNameClosestDist = 130,
        TransparentWindow = false,
        boxOffsetY = 0,
        Antiparalysis = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        Antidote = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        clampItemView = true,
        AlwaysAutoResize = true,
        RareBarrier = {
            showBox = true,
            borderSize = 3,
            showName = true,
            useCustomColor = true,
            highlightMaxStats = true,
            enabled = true,
            customBorderColor = -3787520,
            includeStats = true,
        },
        Trifluid = {
            borderSize = 3,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -10038789,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        Meseta = {
            showName = true,
            borderSize = 2,
            MinAmount = 1,
            useCustomColor = true,
            enabled = true,
            customBorderColor = 15982124,
            showBox = true,
        },
        boxSizeX = 40,
        HPMat = {
            showName = true,
            borderSize = 2,
            customBorderColor = -13107376,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        boxSizeY = 40,
        TechAttackHigh = {
            showName = true,
            borderSize = 4,
            customBorderColor = -14614273,
            useCustomColor = false,
            enabled = true,
            MinLvl = 1,
            showBox = true,
        },
        TechAttack20 = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        RareMag = {
            borderSize = 3,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -49153,
            showBox = true,
        },
        showNameOverride = true,
        TechSupport20 = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        MaxSocketCommonArmor = {
            showBox = true,
            borderSize = 1,
            showName = true,
            useCustomColor = true,
            includeSlots = true,
            enabled = true,
            customBorderColor = -1,
            includeStats = true,
        },
        HideWhenSymbolChat = false,
        Monogrinder = {
            showName = true,
            borderSize = 1,
            customBorderColor = -10997750,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        TechReverser = {
            borderSize = 2,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        MusicDisk = {
            showDisk21 = true,
            borderSize = 2,
            showDisk13 = true,
            showDisk4 = true,
            useCustomColor = true,
            showDisk1 = true,
            showBox = true,
            showDisk24 = true,
            showName = true,
            showDisk7 = true,
            showDisk14 = true,
            showDisk15 = true,
            showDisk11 = true,
            showDisk18 = true,
            showDisk20 = true,
            showDisk6 = true,
            showDisk12 = true,
            showDisk3 = true,
            showDisk10 = true,
            showDisk23 = true,
            showDisk9 = true,
            customBorderColor = -8226174,
            showDisk25 = true,
            showDisk2 = true,
            showDisk17 = true,
            showDisk22 = true,
            showDisk8 = true,
            showDisk19 = true,
            enabled = true,
            showDisk5 = true,
            showDisk16 = true,
        },
        Digrinder = {
            showName = true,
            borderSize = 2,
            customBorderColor = -8040909,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        TechRyuker = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        customTrackerColorMarker = -26368,
        TechAttack15 = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        Trigrinder = {
            showName = true,
            borderSize = 3,
            customBorderColor = -5674936,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        Monomate = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -16747676,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        Telepipe = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        EvadeMat = {
            showName = true,
            borderSize = 2,
            customBorderColor = -4776780,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        MindMat = {
            showName = true,
            borderSize = 2,
            customBorderColor = -14973512,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        HideWhenMenuUnavailable = true,
        customTrackerColorEnable = true,
        CommonUnit = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -38656,
            showBox = false,
        },
        CommonTech = {
            borderSize = 3,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        H = 91,
        customTrackerColorBackground = 13421772,
        RareConsumables = {
            borderSize = 2,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -62966,
            showBox = true,
        },
        TechSupportHigh = {
            showName = true,
            borderSize = 2,
            customBorderColor = -14614273,
            useCustomColor = false,
            enabled = true,
            MinLvl = 1,
            showBox = true,
        },
        HighHitCommonWeapon = {
            HitMin = 40,
            borderSize = 1,
            includeSpecial = true,
            includeHit = true,
            customBorderColor = -38656,
            showBox = false,
            showName = true,
            includeAtrributes = true,
            enabled = true,
            useCustomColor = false,
        },
        TechSupport15 = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        RareWeapon = {
            borderSize = 2,
            includeSpecial = true,
            useCustomColor = true,
            customBorderColor = -62966,
            showBox = true,
            showName = true,
            includeAtrributes = true,
            enabled = true,
            includeHit = true,
        },
        ClairesDeal = {
            borderSize = 1,
            enabled = false,
            showName = true,
            useCustomColor = true,
            customBorderColor = -38656,
            showBox = true,
        },
        ScapeDoll = {
            borderSize = 2,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -43629,
            showBox = true,
        },
        EnableWindow = true,
        RareArmor = {
            borderSize = 3,
            includeSlots = true,
            useCustomColor = true,
            customBorderColor = -65466,
            showBox = true,
            showName = true,
            enabled = true,
            highlightMaxStats = true,
            includeStats = true,
        },
        SolAtomizer = {
            showName = true,
            borderSize = 1,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        showNameClosestItemsNum = 5,
        ESWeapon = {
            borderSize = 6,
            includeSpecial = true,
            useCustomColor = true,
            customBorderColor = -62966,
            showBox = true,
            showName = true,
            includeAtrributes = true,
            enabled = true,
            includeHit = true,
        },
        TechAnti5 = {
            borderSize = 1,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
        CommonArmor = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -38656,
            showBox = false,
        },
        PowerMat = {
            showName = true,
            borderSize = 2,
            customBorderColor = -48063,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        Difluid = {
            borderSize = 2,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -16729931,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        TechGrants = {
            showName = true,
            borderSize = 4,
            customBorderColor = -14614273,
            useCustomColor = true,
            enabled = true,
            MinLvl = 1,
            showBox = true,
        },
        changed = false,
        DefenseMat = {
            showName = true,
            borderSize = 2,
            customBorderColor = -1345495,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        customFontScaleEnabled = false,
        TPMat = {
            borderSize = 4,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -13469475,
            showBox = true,
        },
        customTrackerColorWindow = 0,
        fontScale = 1.400000,
        category = {
        },
        Dimate = {
            showName = true,
            borderSize = 2,
            customBorderColor = -16737931,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        LowHitCommonWeapon = {
            borderSize = 1,
            includeSpecial = true,
            includeHit = true,
            customBorderColor = -1,
            showBox = false,
            showName = true,
            includeAtrributes = true,
            enabled = true,
            useCustomColor = true,
        },
        StarAtomizer = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        LuckMat = {
            showName = true,
            borderSize = 3,
            customBorderColor = -2506,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        MoonAtomizer = {
            showName = true,
            borderSize = 1,
            customBorderColor = -2701629,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        ignoreItemMaxDist = 420,
        TechMegid = {
            showName = true,
            borderSize = 4,
            customBorderColor = -14614273,
            useCustomColor = true,
            enabled = true,
            MinLvl = 1,
            showBox = true,
        },
        CommonBarrier = {
            borderSize = 1,
            useCustomColor = false,
            showName = true,
            enabled = true,
            customBorderColor = -38656,
            showBox = false,
        },
        HideWhenMenu = true,
        Monofluid = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -16742997,
            useCustomColor = true,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        TrapVision = {
            borderSize = 1,
            showName = true,
            onlyShowWhenOneOrMoreInInv = true,
            customBorderColor = -38656,
            useCustomColor = false,
            enabled = true,
            onlyShowIfInvNotMaxStack = true,
            showBox = true,
        },
        W = 271,
        TechAnti7 = {
            borderSize = 1,
            useCustomColor = true,
            showName = true,
            enabled = true,
            customBorderColor = -14614273,
            showBox = true,
        },
    },
    customFoV0 = 86,
    configurationEnableWindow = false,
    customFoV3 = 89,
    customFoV4 = 90,
    customScreenResEnabled = false,
    customFoV2 = 88,
    numTrackers = 25,
    customScreenResY = 1080,
    updateThrottle = 15,
    ignoreMeseta = false,
    customFoVEnabled = false,
    server = 3,
    maxNumTrackers = 100,
}
