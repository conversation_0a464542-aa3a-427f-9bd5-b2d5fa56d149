return
{
    configurationEnableWindow = false,
    enable = true,

    allPlayersEnableWindow = false,
    allHideWhenMenu = true,
    allHideWhenSymbolChat = true,
    allHideWhenMenuUnavailable = true,
    allPlayersChanged = false,
    allPlayersAnchor = 4,
    allPlayersX = 0,
    allPlayersY = 100,
    allPlayersW = 850,
    allPlayersH = 116,
    allPlayersNoTitleBar = "NoTitleBar",
    allPlayersNoResize = "",
    allPlayersNoMove = "",
    allPlayersTransparentWindow = false,
    allPlayersListHorizontal = true,
    allPlayersListMaxLength = 4,
    allPlayersShowIndex = false,
    allPlayersShowName = true,
    allPlayersShowHpBar = true,
    allPlayersShowBuff = true,
    allPlayersShowBuffProgressBar = true,

    singlePlayersEnableWindow = true,
    singlePlayersShowBarText = false,
    singlePlayersShowBarMaxValue = true,

    players = {
        {
            EnableWindow = true,
            HideWhenMenu = true, 
            HideWhenSymbolChat = true, 
            HideWhenMenuUnavailable = nil, 
            ShowName = true,
            ShowHPBar = true,
            SD = true,
            Invulnerability = true,
            Changed = false,
            Anchor = 6,
            X = -420,
            Y = 0,
            W = 106,
            H = 86,
            NoTitleBar = "NoTitleBar",
            NoResize = "NoResize",
            NoMove = "NoMove",
            NoScrollbar = "NoScrollbar",
            AlwaysAutoResize = "",
            TransparentWindow = false,
        },
        {
            EnableWindow = true,
            HideWhenMenu = true, 
            HideWhenSymbolChat = true, 
            HideWhenMenuUnavailable = nil, 
            ShowName = true,
            ShowHPBar = true,
            SD = true,
            Invulnerability = true,
            Changed = false,
            Anchor = 6,
            X = -313,
            Y = -60,
            W = 106,
            H = 86,
            NoTitleBar = "NoTitleBar",
            NoResize = "NoResize",
            NoMove = "NoMove",
            NoScrollbar = "NoScrollbar",
            AlwaysAutoResize = "",
            TransparentWindow = false,
        },
        {
            EnableWindow = true,
            HideWhenMenu = true, 
            HideWhenSymbolChat = true, 
            HideWhenMenuUnavailable = nil, 
            ShowName = true,
            ShowHPBar = true,
            SD = true,
            Invulnerability = true,
            Changed = false,
            Anchor = 6,
            X = -205,
            Y = -60,
            W = 106,
            H = 86,
            NoTitleBar = "NoTitleBar",
            NoResize = "NoResize",
            NoMove = "NoMove",
            NoScrollbar = "NoScrollbar",
            AlwaysAutoResize = "",
            TransparentWindow = false,
        },
        {
            EnableWindow = true,
            HideWhenMenu = true, 
            HideWhenSymbolChat = true, 
            HideWhenMenuUnavailable = nil, 
            ShowName = true,
            ShowHPBar = true,
            SD = true,
            Invulnerability = true,
            Changed = true,
            Anchor = 6,
            X = -97,
            Y = -60,
            W = 106,
            H = 86,
            NoTitleBar = "NoTitleBar",
            NoResize = "NoResize",
            NoMove = "NoMove",
            NoScrollbar = "NoScrollbar",
            AlwaysAutoResize = "",
            TransparentWindow = false,
        },
    },
   myself = {
        EnableWindow = false,
        HideWhenMenu = false,
        HideWhenSymbolChat = false,
        HideWhenMenuUnavailable = false,
        ShowName = false,
        ShowBarText = true,
        ShowBarMaxValue = true,
        ShowHPBar = true,
        SD = true,
        Invulnerability = true,
        Changed = false,
        Anchor = 3,
        X = 5,
        Y = -5,
        W = 100,
        H = 64,
        NoTitleBar = "NoTitleBar",
        NoResize = "NoResize",
        NoMove = "NoMove",
        NoScrollbar = "NoScrollbar",
        AlwaysAutoResize = "",
        TransparentWindow = false,
   },
}
