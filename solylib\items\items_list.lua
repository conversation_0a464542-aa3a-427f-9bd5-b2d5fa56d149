local lib_items_cfg = require("solylib.items.items_configuration")

local t = {}

-- Setting the color to something different than 0 will make the addon use the selected
-- color for the item. For Armors/Units/Weapons, this will also disable the ability for
-- that item to be filtered).
-- Setting the flag to false, will make the addon NOT print the item when its on the floor
local COLOR_DFLT       = 0x00000000
local COLOR_ARMOR      = lib_items_cfg.armorName  -- Not used by default
local COLOR_TOOLS      = lib_items_cfg.toolName
local COLOR_SCAPEDOLL  = lib_items_cfg.orange
local COLOR_GRINDERS   = lib_items_cfg.light_blue
local COLOR_MATS       = lib_items_cfg.orange
local COLOR_MUSIC      = lib_items_cfg.yellow
local COLOR_RARE       = lib_items_cfg.red
local COLOR_GOOD_TECHS = lib_items_cfg.green
local COLOR_UNIT       = lib_items_cfg.unitName   -- Not used by default
local COLOR_WEAPON     = lib_items_cfg.weaponName -- Not used by default

---------------- Weapon
t[ 0x000000 ] = { COLOR_DFLT, true } -- Saber
t[ 0x000100 ] = { COLOR_DFLT, true } -- Saber
t[ 0x000101 ] = { COLOR_DFLT, true } -- Brand
t[ 0x000102 ] = { COLOR_DFLT, true } -- Buster
t[ 0x000103 ] = { COLOR_DFLT, true } -- Pallasch
t[ 0x000104 ] = { COLOR_DFLT, true } -- Gladius
t[ 0x000105 ] = { COLOR_RARE, true } -- DB'S SABER
t[ 0x000106 ] = { COLOR_RARE, true } -- KALADBOLG
t[ 0x000107 ] = { COLOR_RARE, true } -- DURANDAL
t[ 0x000108 ] = { COLOR_RARE, true } -- GALATINE
t[ 0x000200 ] = { COLOR_DFLT, true } -- Sword
t[ 0x000201 ] = { COLOR_DFLT, true } -- Gigush
t[ 0x000202 ] = { COLOR_DFLT, true } -- Breaker
t[ 0x000203 ] = { COLOR_DFLT, true } -- Claymore
t[ 0x000204 ] = { COLOR_DFLT, true } -- Calibur
t[ 0x000205 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD
t[ 0x000206 ] = { COLOR_RARE, true } -- LAST SURVIVOR
t[ 0x000207 ] = { COLOR_RARE, true } -- DRAGON SLAYER
t[ 0x000300 ] = { COLOR_DFLT, true } -- Dagger
t[ 0x000301 ] = { COLOR_DFLT, true } -- Knife
t[ 0x000302 ] = { COLOR_DFLT, true } -- Blade
t[ 0x000303 ] = { COLOR_DFLT, true } -- Edge
t[ 0x000304 ] = { COLOR_DFLT, true } -- Ripper
t[ 0x000305 ] = { COLOR_RARE, true } -- BLADE DANCE
t[ 0x000306 ] = { COLOR_RARE, true } -- BLOODY ART
t[ 0x000307 ] = { COLOR_RARE, true } -- CROSS SCAR
t[ 0x000308 ] = { COLOR_RARE, true } -- ZERO DIVIDE
t[ 0x000309 ] = { COLOR_RARE, true } -- TWO KAMUI
t[ 0x000400 ] = { COLOR_DFLT, true } -- Partisan
t[ 0x000401 ] = { COLOR_DFLT, true } -- Halbert
t[ 0x000402 ] = { COLOR_DFLT, true } -- Glaive
t[ 0x000403 ] = { COLOR_DFLT, true } -- Berdys
t[ 0x000404 ] = { COLOR_DFLT, true } -- Gungnir
t[ 0x000405 ] = { COLOR_RARE, true } -- BRIONAC
t[ 0x000406 ] = { COLOR_RARE, true } -- VJAYA
t[ 0x000407 ] = { COLOR_RARE, true } -- GAE BOLG
t[ 0x000408 ] = { COLOR_RARE, true } -- ASTERON BELT
t[ 0x000500 ] = { COLOR_DFLT, true } -- Slicer
t[ 0x000501 ] = { COLOR_DFLT, true } -- Spinner
t[ 0x000502 ] = { COLOR_DFLT, true } -- Cutter
t[ 0x000503 ] = { COLOR_DFLT, true } -- Sawcer
t[ 0x000504 ] = { COLOR_DFLT, true } -- Diska
t[ 0x000505 ] = { COLOR_RARE, true } -- SLICER OF ASSASSIN
t[ 0x000506 ] = { COLOR_RARE, true } -- DISKA OF LIBERATOR
t[ 0x000507 ] = { COLOR_RARE, true } -- DISKA OF BRAVEMAN
t[ 0x000508 ] = { COLOR_RARE, true } -- IZMAELA
t[ 0x000600 ] = { COLOR_DFLT, true } -- Handgun
t[ 0x000601 ] = { COLOR_DFLT, true } -- Autogun
t[ 0x000602 ] = { COLOR_DFLT, true } -- Lockgun
t[ 0x000603 ] = { COLOR_DFLT, true } -- Railgun
t[ 0x000604 ] = { COLOR_DFLT, true } -- Raygun
t[ 0x000605 ] = { COLOR_RARE, true } -- VARISTA
t[ 0x000606 ] = { COLOR_RARE, true } -- CUSTOM RAY ver.OO
t[ 0x000607 ] = { COLOR_RARE, true } -- BRAVACE
t[ 0x000608 ] = { COLOR_RARE, true } -- TENSION BLASTER
t[ 0x000700 ] = { COLOR_DFLT, true } -- Rifle
t[ 0x000701 ] = { COLOR_DFLT, true } -- Sniper
t[ 0x000702 ] = { COLOR_DFLT, true } -- Blaster
t[ 0x000703 ] = { COLOR_DFLT, true } -- Beam
t[ 0x000704 ] = { COLOR_DFLT, true } -- Laser
t[ 0x000705 ] = { COLOR_RARE, true } -- VISK-235W
t[ 0x000706 ] = { COLOR_RARE, true } -- WALS-MK2
t[ 0x000707 ] = { COLOR_RARE, true } -- JUSTY-23ST
t[ 0x000708 ] = { COLOR_RARE, true } -- RIANOV 303SNR
t[ 0x000709 ] = { COLOR_RARE, true } -- RIANOV 303SNR-1
t[ 0x00070A ] = { COLOR_RARE, true } -- RIANOV 303SNR-2
t[ 0x00070B ] = { COLOR_RARE, true } -- RIANOV 303SNR-3
t[ 0x00070C ] = { COLOR_RARE, true } -- RIANOV 303SNR-4
t[ 0x00070D ] = { COLOR_RARE, true } -- RIANOV 303SNR-5
t[ 0x000800 ] = { COLOR_DFLT, true } -- Mechgun
t[ 0x000801 ] = { COLOR_DFLT, true } -- Assault
t[ 0x000802 ] = { COLOR_DFLT, true } -- Repeater
t[ 0x000803 ] = { COLOR_DFLT, true } -- Gatling
t[ 0x000804 ] = { COLOR_DFLT, true } -- Vulcan
t[ 0x000805 ] = { COLOR_RARE, true } -- M&A60 VISE
t[ 0x000806 ] = { COLOR_RARE, true } -- H&S25 JUSTICE
t[ 0x000807 ] = { COLOR_RARE, true } -- L&K14 COMBAT
t[ 0x000900 ] = { COLOR_DFLT, true } -- Shot
t[ 0x000901 ] = { COLOR_DFLT, true } -- Spread
t[ 0x000902 ] = { COLOR_DFLT, true } -- Cannon
t[ 0x000903 ] = { COLOR_DFLT, true } -- Launcher
t[ 0x000904 ] = { COLOR_DFLT, true } -- Arms
t[ 0x000905 ] = { COLOR_RARE, true } -- CRUSH BULLET
t[ 0x000906 ] = { COLOR_RARE, true } -- METEOR SMASH
t[ 0x000907 ] = { COLOR_RARE, true } -- FINAL IMPACT
t[ 0x000A00 ] = { COLOR_DFLT, true } -- Cane
t[ 0x000A01 ] = { COLOR_DFLT, true } -- Stick
t[ 0x000A02 ] = { COLOR_DFLT, true } -- Mace
t[ 0x000A03 ] = { COLOR_DFLT, true } -- Club
t[ 0x000A04 ] = { COLOR_RARE, true } -- CLUB OF LACONIUM
t[ 0x000A05 ] = { COLOR_RARE, true } -- MACE OF ADAMAN
t[ 0x000A06 ] = { COLOR_RARE, true } -- CLUB OF ZUMIURAN
t[ 0x000A07 ] = { COLOR_RARE, true } -- LOLLIPOP
t[ 0x000B00 ] = { COLOR_DFLT, true } -- Rod
t[ 0x000B01 ] = { COLOR_DFLT, true } -- Pole
t[ 0x000B02 ] = { COLOR_DFLT, true } -- Pillar
t[ 0x000B03 ] = { COLOR_DFLT, true } -- Striker
t[ 0x000B04 ] = { COLOR_RARE, true } -- BATTLE VERGE
t[ 0x000B05 ] = { COLOR_RARE, true } -- BRAVE HAMMER
t[ 0x000B06 ] = { COLOR_RARE, true } -- ALIVE AQHU
t[ 0x000B07 ] = { COLOR_RARE, true } -- VALKYRIE
t[ 0x000C00 ] = { COLOR_DFLT, true } -- Wand
t[ 0x000C01 ] = { COLOR_DFLT, true } -- Staff
t[ 0x000C02 ] = { COLOR_DFLT, true } -- Baton
t[ 0x000C03 ] = { COLOR_DFLT, true } -- Scepter
t[ 0x000C04 ] = { COLOR_RARE, true } -- FIRE SCEPTER:AGNI
t[ 0x000C05 ] = { COLOR_RARE, true } -- ICE STAFF:DAGON
t[ 0x000C06 ] = { COLOR_RARE, true } -- STORM WAND:INDRA
t[ 0x000C07 ] = { COLOR_RARE, true } -- EARTH WAND BROWNIE
t[ 0x000D00 ] = { COLOR_RARE, true } -- PHOTON CLAW
t[ 0x000D01 ] = { COLOR_RARE, true } -- SILENCE CLAW
t[ 0x000D02 ] = { COLOR_RARE, true } -- NEI'S CLAW
t[ 0x000D03 ] = { COLOR_RARE, true } -- PHOENIX CLAW
t[ 0x000E00 ] = { COLOR_RARE, true } -- DOUBLE SABER
t[ 0x000E01 ] = { COLOR_RARE, true } -- STAG CUTLERY
t[ 0x000E02 ] = { COLOR_RARE, true } -- TWIN BRAND
t[ 0x000F00 ] = { COLOR_RARE, true } -- BRAVE KNUCKLE
t[ 0x000F01 ] = { COLOR_RARE, true } -- ANGRY FIST
t[ 0x000F02 ] = { COLOR_RARE, true } -- GOD HAND
t[ 0x000F03 ] = { COLOR_RARE, true } -- SONIC KNUCKLE
t[ 0x000F04 ] = { COLOR_RARE, true } -- LOGiN
t[ 0x001000 ] = { COLOR_RARE, true } -- OROTIAGITO
t[ 0x001001 ] = { COLOR_RARE, true } -- AGITO 1975
t[ 0x001002 ] = { COLOR_RARE, true } -- AGITO 1983
t[ 0x001003 ] = { COLOR_RARE, true } -- AGITO 2001
t[ 0x001004 ] = { COLOR_RARE, true } -- AGITO 1991
t[ 0x001005 ] = { COLOR_RARE, true } -- AGITO 1977
t[ 0x001006 ] = { COLOR_RARE, true } -- AGITO 1980
t[ 0x001007 ] = { COLOR_RARE, true } -- RAIKIRI
t[ 0x001100 ] = { COLOR_RARE, true } -- SOUL EATER
t[ 0x001101 ] = { COLOR_RARE, true } -- SOUL BANISH
t[ 0x001200 ] = { COLOR_RARE, true } -- SPREAD NEEDLE
t[ 0x001300 ] = { COLOR_RARE, true } -- HOLY RAY
t[ 0x001400 ] = { COLOR_RARE, true } -- INFERNO BAZOOKA
t[ 0x001401 ] = { COLOR_RARE, true } -- RAMBLING MAY
t[ 0x001402 ] = { COLOR_RARE, true } -- L&K38 COMBAT
t[ 0x001500 ] = { COLOR_RARE, true } -- FLAME VISIT
t[ 0x001501 ] = { COLOR_RARE, true } -- BURNING VISIT
t[ 0x001600 ] = { COLOR_RARE, true } -- AKIKO'S FRYING PAN
t[ 0x001700 ] = { COLOR_RARE, true } -- SORCERER'S CANE
t[ 0x001800 ] = { COLOR_RARE, true } -- S-BEAT'S BLADE
t[ 0x001900 ] = { COLOR_RARE, true } -- P-ARMS'S BLADE
t[ 0x001A00 ] = { COLOR_RARE, true } -- DELSABER'S BUSTER
t[ 0x001B00 ] = { COLOR_RARE, true } -- BRINGER'S RIFLE
t[ 0x001C00 ] = { COLOR_RARE, true } -- EGG BLASTER
t[ 0x001D00 ] = { COLOR_RARE, true } -- PSYCHO WAND
t[ 0x001E00 ] = { COLOR_RARE, true } -- HEAVEN PUNISHER
t[ 0x001F00 ] = { COLOR_RARE, true } -- LAVIS CANNON
t[ 0x002000 ] = { COLOR_RARE, true } -- VICTOR AXE
t[ 0x002001 ] = { COLOR_RARE, true } -- LACONIUM AXE
t[ 0x002100 ] = { COLOR_RARE, true } -- CHAIN SAWD
t[ 0x002200 ] = { COLOR_RARE, true } -- CADUCEUS
t[ 0x002201 ] = { COLOR_RARE, true } -- MERCURIUS ROD
t[ 0x002300 ] = { COLOR_RARE, true } -- STING TIP
t[ 0x002400 ] = { COLOR_RARE, true } -- MAGICAL PIECE
t[ 0x002500 ] = { COLOR_RARE, true } -- TECHNICAL CROZIER
t[ 0x002600 ] = { COLOR_RARE, true } -- SUPPRESSED GUN
t[ 0x002700 ] = { COLOR_RARE, true } -- ANCIENT SABER
t[ 0x002800 ] = { COLOR_RARE, true } -- HARISEN BATTLE FAN
t[ 0x002900 ] = { COLOR_RARE, true } -- YAMIGARASU
t[ 0x002A00 ] = { COLOR_RARE, true } -- AKIKO'S WOK
t[ 0x002B00 ] = { COLOR_RARE, true } -- TOY HAMMER
t[ 0x002C00 ] = { COLOR_RARE, true } -- ELYSION
t[ 0x002D00 ] = { COLOR_RARE, true } -- RED SABER
t[ 0x002E00 ] = { COLOR_RARE, true } -- METEOR CUDGEL
t[ 0x002F00 ] = { COLOR_RARE, true } -- MONKEY KING BAR
t[ 0x002F01 ] = { COLOR_RARE, true } -- BLACK KING BAR
t[ 0x003000 ] = { COLOR_RARE, true } -- DOUBLE CANNON
t[ 0x003001 ] = { COLOR_RARE, true } -- GIRASOLE
t[ 0x003100 ] = { COLOR_RARE, true } -- HUGE BATTLE FAN
t[ 0x003200 ] = { COLOR_RARE, true } -- TSUMIKIRI J-SWORD
t[ 0x003300 ] = { COLOR_RARE, true } -- SEALED J-SWORD
t[ 0x003400 ] = { COLOR_RARE, true } -- RED SWORD
t[ 0x003500 ] = { COLOR_RARE, true } -- CRAZY TUNE
t[ 0x003600 ] = { COLOR_RARE, true } -- TWIN CHAKRAM
t[ 0x003700 ] = { COLOR_RARE, true } -- WOK OF AKIKO'S SHOP
t[ 0x003800 ] = { COLOR_RARE, true } -- LAVIS BLADE
t[ 0x003900 ] = { COLOR_RARE, true } -- RED DAGGER
t[ 0x003A00 ] = { COLOR_RARE, true } -- MADAM'S PARASOL
t[ 0x003B00 ] = { COLOR_RARE, true } -- MADAM'S UMBRELLA
t[ 0x003C00 ] = { COLOR_RARE, true } -- IMPERIAL PICK
t[ 0x003D00 ] = { COLOR_RARE, true } -- BERDYSH
t[ 0x003E00 ] = { COLOR_RARE, true } -- RED PARTISAN
t[ 0x003F00 ] = { COLOR_RARE, true } -- FLIGHT CUTTER
t[ 0x004000 ] = { COLOR_RARE, true } -- FLIGHT FAN
t[ 0x004100 ] = { COLOR_RARE, true } -- RED SLICER
t[ 0x004200 ] = { COLOR_RARE, true } -- HANDGUN:GULD
t[ 0x004201 ] = { COLOR_RARE, true } -- MASTER RAVEN
t[ 0x004300 ] = { COLOR_RARE, true } -- HANDGUN:MILLA
t[ 0x004301 ] = { COLOR_RARE, true } -- LAST SWAN
t[ 0x004400 ] = { COLOR_RARE, true } -- RED HANDGUN
t[ 0x004500 ] = { COLOR_RARE, true } -- FROZEN SHOOTER
t[ 0x004501 ] = { COLOR_RARE, true } -- SNOW QUEEN
t[ 0x004600 ] = { COLOR_RARE, true } -- ANTI ANDROID RIFLE
t[ 0x004700 ] = { COLOR_RARE, true } -- ROCKET PUNCH
t[ 0x004800 ] = { COLOR_RARE, true } -- SAMBA MARACAS
t[ 0x004900 ] = { COLOR_RARE, true } -- TWIN PSYCHOGUN
t[ 0x004A00 ] = { COLOR_RARE, true } -- DRILL LAUNCHER
t[ 0x004B00 ] = { COLOR_RARE, true } -- GULD MILLA
t[ 0x004B01 ] = { COLOR_RARE, true } -- DUAL BIRD
t[ 0x004C00 ] = { COLOR_RARE, true } -- RED MECHGUN
t[ 0x004D00 ] = { COLOR_RARE, true } -- BELRA CANNON
t[ 0x004E00 ] = { COLOR_RARE, true } -- PANZER FAUST
t[ 0x004E01 ] = { COLOR_RARE, true } -- IRON FAUST
t[ 0x004F00 ] = { COLOR_RARE, true } -- SUMMIT MOON
t[ 0x005000 ] = { COLOR_RARE, true } -- WINDMILL
t[ 0x005100 ] = { COLOR_RARE, true } -- EVIL CURST
t[ 0x005200 ] = { COLOR_RARE, true } -- FLOWER CANE
t[ 0x005300 ] = { COLOR_RARE, true } -- HILDEBEAR'S CANE
t[ 0x005400 ] = { COLOR_RARE, true } -- HILDEBLUE'S CANE
t[ 0x005500 ] = { COLOR_RARE, true } -- RABBIT WAND
t[ 0x005600 ] = { COLOR_RARE, true } -- PLANTAIN LEAF
t[ 0x005601 ] = { COLOR_RARE, true } -- FATSIA
t[ 0x005700 ] = { COLOR_RARE, true } -- DEMONIC FORK
t[ 0x005800 ] = { COLOR_RARE, true } -- STRIKER OF CHAO
t[ 0x005900 ] = { COLOR_RARE, true } -- BROOM
t[ 0x005A00 ] = { COLOR_RARE, true } -- PROPHETS OF MOTAV
t[ 0x005B00 ] = { COLOR_RARE, true } -- THE SIGH OF A GOD
t[ 0x005C00 ] = { COLOR_RARE, true } -- TWINKLE STAR
t[ 0x005D00 ] = { COLOR_RARE, true } -- PLANTAIN FAN
t[ 0x005E00 ] = { COLOR_RARE, true } -- TWIN BLAZE
t[ 0x005F00 ] = { COLOR_RARE, true } -- MARINA'S BAG
t[ 0x006000 ] = { COLOR_RARE, true } -- DRAGON'S CLAW
t[ 0x006100 ] = { COLOR_RARE, true } -- PANTHER'S CLAW
t[ 0x006200 ] = { COLOR_RARE, true } -- S-RED'S BLADE
t[ 0x006300 ] = { COLOR_RARE, true } -- PLANTAIN HUGE FAN
t[ 0x006400 ] = { COLOR_RARE, true } -- CHAMELEON SCYTHE
t[ 0x006500 ] = { COLOR_RARE, true } -- YASMINKOV 3000R
t[ 0x006600 ] = { COLOR_RARE, true } -- ANO RIFLE
t[ 0x006700 ] = { COLOR_RARE, true } -- BARANZ LAUNCHER
t[ 0x006800 ] = { COLOR_RARE, true } -- BRANCH OF PAKUPAKU
t[ 0x006900 ] = { COLOR_RARE, true } -- HEART OF POUMN
t[ 0x006A00 ] = { COLOR_RARE, true } -- YASMINKOV 2000H
t[ 0x006B00 ] = { COLOR_RARE, true } -- YASMINKOV 7000V
t[ 0x006C00 ] = { COLOR_RARE, true } -- YASMINKOV 9000M
t[ 0x006D00 ] = { COLOR_RARE, true } -- MASER BEAM
t[ 0x006D01 ] = { COLOR_RARE, true } -- POWER MASER
t[ 0x006E00 ] = { COLOR_RARE, true } -- GAME MAGAZNE
t[ 0x006E01 ] = { COLOR_RARE, true } -- LOGiN
t[ 0x006F00 ] = { COLOR_RARE, true } -- FLOWER BOUQUET
-- NO SRANKS
t[ 0x008900 ] = { COLOR_RARE, true } -- MUSASHI
t[ 0x008901 ] = { COLOR_RARE, true } -- YAMATO
t[ 0x008902 ] = { COLOR_RARE, true } -- ASUKA
t[ 0x008903 ] = { COLOR_RARE, true } -- SANGE & YASHA
t[ 0x008A00 ] = { COLOR_RARE, true } -- SANGE
t[ 0x008A01 ] = { COLOR_RARE, true } -- YASHA
t[ 0x008A02 ] = { COLOR_RARE, true } -- KAMUI
t[ 0x008B00 ] = { COLOR_RARE, true } -- PHOTON LAUNCHER
t[ 0x008B01 ] = { COLOR_RARE, true } -- GUILTY LIGHT
t[ 0x008B02 ] = { COLOR_RARE, true } -- RED SCORPIO
t[ 0x008B03 ] = { COLOR_RARE, true } -- PHONON MASER
t[ 0x008C00 ] = { COLOR_RARE, true } -- TALIS
t[ 0x008C01 ] = { COLOR_RARE, true } -- MAHU
t[ 0x008C02 ] = { COLOR_RARE, true } -- HITOGATA
t[ 0x008C03 ] = { COLOR_RARE, true } -- DANCING HITOGATA
t[ 0x008C04 ] = { COLOR_RARE, true } -- KUNAI
t[ 0x008D00 ] = { COLOR_RARE, true } -- NUG2000-BAZOOKA
t[ 0x008E00 ] = { COLOR_RARE, true } -- S-BERILL'S HANDS #0
t[ 0x008E01 ] = { COLOR_RARE, true } -- S-BERILL'S HANDS #1
t[ 0x008F00 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3060
t[ 0x008F01 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3064
t[ 0x008F02 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3067
t[ 0x008F03 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3073
t[ 0x008F04 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3077
t[ 0x008F05 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3082
t[ 0x008F06 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3083
t[ 0x008F07 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3084
t[ 0x008F08 ] = { COLOR_RARE, true } -- FLOWEN'S SWORD 3079
t[ 0x009000 ] = { COLOR_RARE, true } -- DB'S SABER 3062
t[ 0x009001 ] = { COLOR_RARE, true } -- DB'S SABER 3067
t[ 0x009002 ] = { COLOR_RARE, true } -- DB'S SABER 3069 Chris
t[ 0x009003 ] = { COLOR_RARE, true } -- DB'S SABER 3064
t[ 0x009004 ] = { COLOR_RARE, true } -- DB'S SABER 3069 Torato
t[ 0x009005 ] = { COLOR_RARE, true } -- DB'S SABER 3073
t[ 0x009006 ] = { COLOR_RARE, true } -- DB'S SABER 3070
t[ 0x009007 ] = { COLOR_RARE, true } -- DB'S SABER 3075
t[ 0x009008 ] = { COLOR_RARE, true } -- DB'S SABER 3077
t[ 0x009100 ] = { COLOR_RARE, true } -- GI GUE BAZOOKA
t[ 0x009200 ] = { COLOR_RARE, true } -- GUARDIANNA
t[ 0x009300 ] = { COLOR_RARE, true } -- VIRIDIA CARD
t[ 0x009301 ] = { COLOR_RARE, true } -- GREENILL CARD
t[ 0x009302 ] = { COLOR_RARE, true } -- SKYLY CARD
t[ 0x009303 ] = { COLOR_RARE, true } -- BLUEFULL CARD
t[ 0x009304 ] = { COLOR_RARE, true } -- PURPLENUM CARD
t[ 0x009305 ] = { COLOR_RARE, true } -- PINKAL CARD
t[ 0x009306 ] = { COLOR_RARE, true } -- REDRIA CARD
t[ 0x009307 ] = { COLOR_RARE, true } -- ORAN CARD
t[ 0x009308 ] = { COLOR_RARE, true } -- YELLOWBOZE CARD
t[ 0x009309 ] = { COLOR_RARE, true } -- WHITILL CARD
t[ 0x009400 ] = { COLOR_RARE, true } -- MORNING GLORY
t[ 0x009500 ] = { COLOR_RARE, true } -- PARTISAN of LIGHTNING
t[ 0x009600 ] = { COLOR_RARE, true } -- GAL WIND
t[ 0x009700 ] = { COLOR_RARE, true } -- ZANBA
t[ 0x009800 ] = { COLOR_RARE, true } -- RIKA'S CLAW
t[ 0x009900 ] = { COLOR_RARE, true } -- ANGEL HARP
t[ 0x009A00 ] = { COLOR_RARE, true } -- DEMOLITION COMET
t[ 0x009B00 ] = { COLOR_RARE, true } -- NEI'S CLAW
t[ 0x009C00 ] = { COLOR_RARE, true } -- RAINBOW BATON
t[ 0x009D00 ] = { COLOR_RARE, true } -- DARK FLOW
t[ 0x009E00 ] = { COLOR_RARE, true } -- DARK METEOR
t[ 0x009F00 ] = { COLOR_RARE, true } -- DARK BRIDGE
t[ 0x00A000 ] = { COLOR_RARE, true } -- G-ASSASSIN'S SABERS
t[ 0x00A100 ] = { COLOR_RARE, true } -- RAPPY'S FAN
t[ 0x00A200 ] = { COLOR_RARE, true } -- BOOMA'S CLAW
t[ 0x00A201 ] = { COLOR_RARE, true } -- GOBOOMA'S CLAW
t[ 0x00A202 ] = { COLOR_RARE, true } -- GIGOBOOMA'S CLAW
t[ 0x00A300 ] = { COLOR_RARE, true } -- RUBY BULLET
t[ 0x00A400 ] = { COLOR_RARE, true } -- AMORE ROSE
-- NO SRANKS
t[ 0x00AA00 ] = { COLOR_RARE, true } -- SLICER OF FANATIC
t[ 0x00AB00 ] = { COLOR_RARE, true } -- LAME D'ARGENT
t[ 0x00AC00 ] = { COLOR_RARE, true } -- EXCALIBUR
t[ 0x00AD00 ] = { COLOR_RARE, true } -- RAGE DE FEU
t[ 0x00AD01 ] = { COLOR_RARE, true } -- RAGE DE FEU
t[ 0x00AD02 ] = { COLOR_RARE, true } -- RAGE DE FEU
t[ 0x00AD03 ] = { COLOR_RARE, true } -- RAGE DE FEU
t[ 0x00AE00 ] = { COLOR_RARE, true } -- DAISY CHAIN
t[ 0x00AF00 ] = { COLOR_RARE, true } -- OPHELIE SEIZE
t[ 0x00B000 ] = { COLOR_RARE, true } -- MILLE MARTEAUX
t[ 0x00B100 ] = { COLOR_RARE, true } -- LE COGNEUR
t[ 0x00B200 ] = { COLOR_RARE, true } -- COMMANDER BLADE
t[ 0x00B300 ] = { COLOR_RARE, true } -- VIVIENNE
t[ 0x00B400 ] = { COLOR_RARE, true } -- KUSANAGI
t[ 0x00B500 ] = { COLOR_RARE, true } -- SACRED DUSTER
t[ 0x00B600 ] = { COLOR_RARE, true } -- GUREN
t[ 0x00B700 ] = { COLOR_RARE, true } -- SHOUREN
t[ 0x00B800 ] = { COLOR_RARE, true } -- JIZAI
t[ 0x00B900 ] = { COLOR_RARE, true } -- FLAMBERGE
t[ 0x00BA00 ] = { COLOR_RARE, true } -- YUNCHANG
t[ 0x00BB00 ] = { COLOR_RARE, true } -- SNAKE SPIRE
t[ 0x00BC00 ] = { COLOR_RARE, true } -- FLAPJACK FLAPPER
t[ 0x00BD00 ] = { COLOR_RARE, true } -- GETSUGASAN
t[ 0x00BE00 ] = { COLOR_RARE, true } -- MAGUWA
t[ 0x00BF00 ] = { COLOR_RARE, true } -- HEAVEN STRIKER
t[ 0x00C000 ] = { COLOR_RARE, true } -- CANNON ROUGE
t[ 0x00C100 ] = { COLOR_RARE, true } -- METEOR ROUGE
t[ 0x00C200 ] = { COLOR_RARE, true } -- SOLFERINO
t[ 0x00C300 ] = { COLOR_RARE, true } -- CLIO
t[ 0x00C400 ] = { COLOR_RARE, true } -- SIREN GLASS HAMMER
t[ 0x00C500 ] = { COLOR_RARE, true } -- GLIDE DIVINE
t[ 0x00C600 ] = { COLOR_RARE, true } -- SHICHISHITO
t[ 0x00C700 ] = { COLOR_RARE, true } -- MURASAME
t[ 0x00C800 ] = { COLOR_RARE, true } -- DAYLIGHT SCAR
t[ 0x00C900 ] = { COLOR_RARE, true } -- DECALOG
t[ 0x00CA00 ] = { COLOR_RARE, true } -- 5TH ANNIV. BLADE
t[ 0x00CB00 ] = { COLOR_RARE, true } -- TYRELL'S PARASOL
t[ 0x00CC00 ] = { COLOR_RARE, true } -- AKIKO'S CLEAVER
t[ 0x00CD00 ] = { COLOR_RARE, true } -- TANEGASHIMA
t[ 0x00CE00 ] = { COLOR_RARE, true } -- TREE CLIPPERS
t[ 0x00CF00 ] = { COLOR_RARE, true } -- NICE SHOT
t[ 0x00D000 ] = { COLOR_RARE, true } -- UNKNOWN3
t[ 0x00D100 ] = { COLOR_RARE, true } -- UNKNOWN4
t[ 0x00D200 ] = { COLOR_RARE, true } -- ANO BAZOOKA
t[ 0x00D300 ] = { COLOR_RARE, true } -- SYNTHESIZER
t[ 0x00D400 ] = { COLOR_RARE, true } -- BAMBOO SPEAR
t[ 0x00D500 ] = { COLOR_RARE, true } -- KAN'EI TSUHO
t[ 0x00D600 ] = { COLOR_RARE, true } -- JITTE
t[ 0x00D700 ] = { COLOR_RARE, true } -- BUTTERFLY NET
t[ 0x00D800 ] = { COLOR_RARE, true } -- SYRINGE
t[ 0x00D900 ] = { COLOR_RARE, true } -- BATTLEDORE
t[ 0x00DA00 ] = { COLOR_RARE, true } -- RACKET
t[ 0x00DB00 ] = { COLOR_RARE, true } -- HAMMER
t[ 0x00DC00 ] = { COLOR_RARE, true } -- GREAT BOUQUET
t[ 0x00DD00 ] = { COLOR_RARE, true } -- TypeSA/SABER
t[ 0x00DE00 ] = { COLOR_RARE, true } -- TypeSL/SABER
t[ 0x00DE01 ] = { COLOR_RARE, true } -- TypeSL/SLICER
t[ 0x00DE02 ] = { COLOR_RARE, true } -- TypeSL/CLAW
t[ 0x00DE03 ] = { COLOR_RARE, true } -- TypeSL/KATANA
t[ 0x00DF00 ] = { COLOR_RARE, true } -- TypeJS/SABER
t[ 0x00DF01 ] = { COLOR_RARE, true } -- TypeJS/SLICER
t[ 0x00DF02 ] = { COLOR_RARE, true } -- TypeJS/J-SWORD
t[ 0x00E000 ] = { COLOR_RARE, true } -- TypeSW/SWORD
t[ 0x00E001 ] = { COLOR_RARE, true } -- TypeSW/SLICER
t[ 0x00E002 ] = { COLOR_RARE, true } -- TypeSW/J-SWORD
t[ 0x00E100 ] = { COLOR_RARE, true } -- TypeRO/SWORD
t[ 0x00E101 ] = { COLOR_RARE, true } -- TypeRO/HALBERT
t[ 0x00E102 ] = { COLOR_RARE, true } -- TypeRO/ROD
t[ 0x00E200 ] = { COLOR_RARE, true } -- TypeBL/BLADE
t[ 0x00E300 ] = { COLOR_RARE, true } -- TypeKN/BLADE
t[ 0x00E301 ] = { COLOR_RARE, true } -- TypeKN/CLAW
t[ 0x00E400 ] = { COLOR_RARE, true } -- TypeHA/HALBERT
t[ 0x00E401 ] = { COLOR_RARE, true } -- TypeHA/ROD
t[ 0x00E500 ] = { COLOR_RARE, true } -- TypeDS/D.SABER
t[ 0x00E501 ] = { COLOR_RARE, true } -- TypeDS/ROD
t[ 0x00E502 ] = { COLOR_RARE, true } -- TypeDS
t[ 0x00E600 ] = { COLOR_RARE, true } -- TypeCL/CLAW
t[ 0x00E700 ] = { COLOR_RARE, true } -- TypeSS/SW
t[ 0x00E800 ] = { COLOR_RARE, true } -- TypeGU/HAND
t[ 0x00E801 ] = { COLOR_RARE, true } -- TypeGU/MECHGUN
t[ 0x00E900 ] = { COLOR_RARE, true } -- TypeRI/RIFLE
t[ 0x00EA00 ] = { COLOR_RARE, true } -- TypeME/MECHGUN
t[ 0x00EB00 ] = { COLOR_RARE, true } -- TypeSH/SHOT
t[ 0x00EC00 ] = { COLOR_RARE, true } -- TypeWA/WAND
t[ 0x00ED00 ] = { COLOR_DFLT, true } -- ????
---------------- Frame
t[ 0x010100 ] = { COLOR_DFLT, true } -- Frame
t[ 0x010101 ] = { COLOR_DFLT, true } -- Armor
t[ 0x010102 ] = { COLOR_DFLT, true } -- Psy Armor
t[ 0x010103 ] = { COLOR_DFLT, true } -- Giga Frame
t[ 0x010104 ] = { COLOR_DFLT, true } -- Soul Frame
t[ 0x010105 ] = { COLOR_DFLT, true } -- Cross Armor
t[ 0x010106 ] = { COLOR_DFLT, true } -- Solid Frame
t[ 0x010107 ] = { COLOR_DFLT, true } -- Brave Armor
t[ 0x010108 ] = { COLOR_DFLT, true } -- Hyper Frame
t[ 0x010109 ] = { COLOR_DFLT, true } -- Grand Armor
t[ 0x01010A ] = { COLOR_DFLT, true } -- Shock Frame
t[ 0x01010B ] = { COLOR_DFLT, true } -- King's Frame
t[ 0x01010C ] = { COLOR_DFLT, true } -- Dragon Frame
t[ 0x01010D ] = { COLOR_DFLT, true } -- Absorb Armor
t[ 0x01010E ] = { COLOR_DFLT, true } -- Protect Frame
t[ 0x01010F ] = { COLOR_DFLT, true } -- General Armor
t[ 0x010110 ] = { COLOR_DFLT, true } -- Perfect Frame
t[ 0x010111 ] = { COLOR_DFLT, true } -- Valiant Frame
t[ 0x010112 ] = { COLOR_DFLT, true } -- Imperial Armor
t[ 0x010113 ] = { COLOR_DFLT, true } -- Holiness Armor
t[ 0x010114 ] = { COLOR_DFLT, true } -- Guardian Armor
t[ 0x010115 ] = { COLOR_DFLT, true } -- Divinity Armor
t[ 0x010116 ] = { COLOR_DFLT, true } -- Ultimate Frame
t[ 0x010117 ] = { COLOR_DFLT, true } -- Celestial Armor
t[ 0x010118 ] = { COLOR_RARE, true } -- HUNTER FIELD
t[ 0x010119 ] = { COLOR_RARE, true } -- RANGER FIELD
t[ 0x01011A ] = { COLOR_RARE, true } -- FORCE FIELD
t[ 0x01011B ] = { COLOR_RARE, true } -- REVIVAL GARMENT
t[ 0x01011C ] = { COLOR_RARE, true } -- SPIRIT GARMENT
t[ 0x01011D ] = { COLOR_RARE, true } -- STINK FRAME
t[ 0x01011E ] = { COLOR_RARE, true } -- D-PARTS ver1.01
t[ 0x01011F ] = { COLOR_RARE, true } -- D-PARTS ver2.10
t[ 0x010120 ] = { COLOR_RARE, true } -- PARASITE WEAR:De Rol
t[ 0x010121 ] = { COLOR_RARE, true } -- PARASITE WEAR:Nelgal
t[ 0x010122 ] = { COLOR_RARE, true } -- PARASITE WEAR:Vajulla
t[ 0x010123 ] = { COLOR_RARE, true } -- SENSE PLATE
t[ 0x010124 ] = { COLOR_RARE, true } -- GRAVITON PLATE
t[ 0x010125 ] = { COLOR_RARE, true } -- ATTRIBUTE PLATE
t[ 0x010126 ] = { COLOR_RARE, true } -- FLOWEN'S FRAME
t[ 0x010127 ] = { COLOR_RARE, true } -- CUSTOM FRAME ver.OO
t[ 0x010128 ] = { COLOR_RARE, true } -- DB'S ARMOR
t[ 0x010129 ] = { COLOR_RARE, true } -- GUARD WAVE
t[ 0x01012A ] = { COLOR_RARE, true } -- DF FIELD
t[ 0x01012B ] = { COLOR_RARE, true } -- LUMINOUS FIELD
t[ 0x01012C ] = { COLOR_RARE, true } -- CHU CHU FEVER
t[ 0x01012D ] = { COLOR_RARE, true } -- LOVE HEART
t[ 0x01012E ] = { COLOR_RARE, true } -- FLAME GARMENT
t[ 0x01012F ] = { COLOR_RARE, true } -- VIRUS ARMOR:Lafuteria
t[ 0x010130 ] = { COLOR_RARE, true } -- BRIGHTNESS CIRCLE
t[ 0x010131 ] = { COLOR_RARE, true } -- AURA FIELD
t[ 0x010132 ] = { COLOR_RARE, true } -- ELECTRO FRAME
t[ 0x010133 ] = { COLOR_RARE, true } -- SACRED CLOTH
t[ 0x010134 ] = { COLOR_RARE, true } -- SMOKING PLATE
t[ 0x010135 ] = { COLOR_RARE, true } -- STAR CUIRASS
t[ 0x010136 ] = { COLOR_RARE, true } -- BLACK HOUND CUIRASS
t[ 0x010137 ] = { COLOR_RARE, true } -- MORNING PRAYER
t[ 0x010138 ] = { COLOR_RARE, true } -- BLACK ODOSHI DOMARU
t[ 0x010139 ] = { COLOR_RARE, true } -- RED ODOSHI DOMARU
t[ 0x01013A ] = { COLOR_RARE, true } -- BLACK ODOSHI RED NIMAIDOU
t[ 0x01013B ] = { COLOR_RARE, true } -- BLUE ODOSHI VIOLET NIMAIDOU
t[ 0x01013C ] = { COLOR_RARE, true } -- DIRTY LIFEJACKET
t[ 0x01013D ] = { COLOR_RARE, true } -- KROE'S SWEATER
t[ 0x01013E ] = { COLOR_RARE, true } -- WEDDING DRESS
t[ 0x01013F ] = { COLOR_RARE, true } -- SONICTEAM ARMOR
t[ 0x010140 ] = { COLOR_RARE, true } -- RED COAT
t[ 0x010141 ] = { COLOR_RARE, true } -- THIRTEEN
t[ 0x010142 ] = { COLOR_RARE, true } -- MOTHER GARB
t[ 0x010143 ] = { COLOR_RARE, true } -- MOTHER GARB+
t[ 0x010144 ] = { COLOR_RARE, true } -- DRESS PLATE
t[ 0x010145 ] = { COLOR_RARE, true } -- SWEETHEART
t[ 0x010146 ] = { COLOR_RARE, true } -- IGNITION CLOAK
t[ 0x010147 ] = { COLOR_RARE, true } -- CONGEAL CLOAK
t[ 0x010148 ] = { COLOR_RARE, true } -- TEMPEST CLOAK
t[ 0x010149 ] = { COLOR_RARE, true } -- CURSED CLOAK
t[ 0x01014A ] = { COLOR_RARE, true } -- SELECT CLOAK
t[ 0x01014B ] = { COLOR_RARE, true } -- SPIRIT CUIRASS
t[ 0x01014C ] = { COLOR_RARE, true } -- REVIVAL CURIASS
t[ 0x01014D ] = { COLOR_RARE, true } -- ALLIANCE UNIFORM
t[ 0x01014E ] = { COLOR_RARE, true } -- OFFICER UNIFORM
t[ 0x01014F ] = { COLOR_RARE, true } -- COMMANDER UNIFORM
t[ 0x010150 ] = { COLOR_RARE, true } -- CRIMSON COAT
t[ 0x010151 ] = { COLOR_RARE, true } -- INFANTRY GEAR
t[ 0x010152 ] = { COLOR_RARE, true } -- LIEUTENANT GEAR
t[ 0x010153 ] = { COLOR_RARE, true } -- INFANTRY MANTLE
t[ 0x010154 ] = { COLOR_RARE, true } -- LIEUTENANT MANTLE
t[ 0x010155 ] = { COLOR_RARE, true } -- UNION FIELD
t[ 0x010156 ] = { COLOR_RARE, true } -- SAMURAI ARMOR
t[ 0x010157 ] = { COLOR_RARE, true } -- STEALTH SUIT
t[ 0x010158 ] = { COLOR_DFLT, true } -- ????
---------------- Barrier
t[ 0x010200 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x010201 ] = { COLOR_DFLT, true } -- Shield
t[ 0x010202 ] = { COLOR_DFLT, true } -- Core Shield
t[ 0x010203 ] = { COLOR_DFLT, true } -- Giga Shield
t[ 0x010204 ] = { COLOR_DFLT, true } -- Soul Barrier
t[ 0x010205 ] = { COLOR_DFLT, true } -- Hard Shield
t[ 0x010206 ] = { COLOR_DFLT, true } -- Brave Barrier
t[ 0x010207 ] = { COLOR_DFLT, true } -- Solid Shield
t[ 0x010208 ] = { COLOR_DFLT, true } -- Flame Barrier
t[ 0x010209 ] = { COLOR_DFLT, true } -- Plasma Barrier
t[ 0x01020A ] = { COLOR_DFLT, true } -- Freeze Barrier
t[ 0x01020B ] = { COLOR_DFLT, true } -- Psychic Barrier
t[ 0x01020C ] = { COLOR_DFLT, true } -- General Shield
t[ 0x01020D ] = { COLOR_DFLT, true } -- Protect Barrier
t[ 0x01020E ] = { COLOR_DFLT, true } -- Glorious Shield
t[ 0x01020F ] = { COLOR_DFLT, true } -- Imperial Barrier
t[ 0x010210 ] = { COLOR_DFLT, true } -- Guardian Shield
t[ 0x010211 ] = { COLOR_DFLT, true } -- Divinity Barrier
t[ 0x010212 ] = { COLOR_DFLT, true } -- Ultimate Shield
t[ 0x010213 ] = { COLOR_DFLT, true } -- Spiritual Shield
t[ 0x010214 ] = { COLOR_DFLT, true } -- Celestial Shield
t[ 0x010215 ] = { COLOR_RARE, true } -- INVISIBLE GUARD
t[ 0x010216 ] = { COLOR_RARE, true } -- SACRED GUARD
t[ 0x010217 ] = { COLOR_RARE, true } -- S-PARTS ver1.16
t[ 0x010218 ] = { COLOR_RARE, true } -- S-PARTS ver2.01
t[ 0x010219 ] = { COLOR_RARE, true } -- LIGHT RELIEF
t[ 0x01021A ] = { COLOR_RARE, true } -- SHIELD OF DELSABER
t[ 0x01021B ] = { COLOR_RARE, true } -- FORCE WALL
t[ 0x01021C ] = { COLOR_RARE, true } -- RANGER WALL
t[ 0x01021D ] = { COLOR_RARE, true } -- HUNTER WALL
t[ 0x01021E ] = { COLOR_RARE, true } -- ATTRIBUTE WALL
t[ 0x01021F ] = { COLOR_RARE, true } -- SECRET GEAR
t[ 0x010220 ] = { COLOR_RARE, true } -- COMBAT GEAR
t[ 0x010221 ] = { COLOR_RARE, true } -- PROTO REGENE GEAR
t[ 0x010222 ] = { COLOR_RARE, true } -- REGENERATE GEAR
t[ 0x010223 ] = { COLOR_RARE, true } -- REGENE GEAR ADV.
t[ 0x010224 ] = { COLOR_RARE, true } -- FLOWEN'S SHIELD
t[ 0x010225 ] = { COLOR_RARE, true } -- CUSTOM BARRIER ver.OO
t[ 0x010226 ] = { COLOR_RARE, true } -- DB'S SHIELD
t[ 0x010227 ] = { COLOR_RARE, true } -- RED RING
t[ 0x010228 ] = { COLOR_RARE, true } -- TRIPOLIC SHIELD
t[ 0x010229 ] = { COLOR_RARE, true } -- STANDSTILL SHIELD
t[ 0x01022A ] = { COLOR_RARE, true } -- SAFETY HEART
t[ 0x01022B ] = { COLOR_RARE, true } -- KASAMI BRACER
t[ 0x01022C ] = { COLOR_RARE, true } -- GODS SHIELD "SUZAKU"
t[ 0x01022D ] = { COLOR_RARE, true } -- GODS SHIELD "GENBU"
t[ 0x01022E ] = { COLOR_RARE, true } -- GODS SHIELD "BYAKKO"
t[ 0x01022F ] = { COLOR_RARE, true } -- GODS SHIELD "SEIRYU"
t[ 0x010230 ] = { COLOR_RARE, true } -- HUNTER'S SHELL
t[ 0x010231 ] = { COLOR_RARE, true } -- RICO'S GLASSES
t[ 0x010232 ] = { COLOR_RARE, true } -- RICO'S EARRING
t[ 0x010233 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010234 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x010235 ] = { COLOR_RARE, true } -- SECURE FEET
t[ 0x010236 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x010237 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x010238 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x010239 ] = { COLOR_DFLT, true } -- Barrier
t[ 0x01023A ] = { COLOR_RARE, true } -- RESTA MERGE
t[ 0x01023B ] = { COLOR_RARE, true } -- ANTI MERGE
t[ 0x01023C ] = { COLOR_RARE, true } -- SHIFTA MERGE
t[ 0x01023D ] = { COLOR_RARE, true } -- DEBAND MERGE
t[ 0x01023E ] = { COLOR_RARE, true } -- FOIE MERGE
t[ 0x01023F ] = { COLOR_RARE, true } -- GIFOIE MERGE
t[ 0x010240 ] = { COLOR_RARE, true } -- RAFOIE MERGE
t[ 0x010241 ] = { COLOR_RARE, true } -- RED MERGE
t[ 0x010242 ] = { COLOR_RARE, true } -- BARTA MERGE
t[ 0x010243 ] = { COLOR_RARE, true } -- GIBARTA MERGE
t[ 0x010244 ] = { COLOR_RARE, true } -- RABARTA MERGE
t[ 0x010245 ] = { COLOR_RARE, true } -- BLUE MERGE
t[ 0x010246 ] = { COLOR_RARE, true } -- ZONDE MERGE
t[ 0x010247 ] = { COLOR_RARE, true } -- GIZONDE MERGE
t[ 0x010248 ] = { COLOR_RARE, true } -- RAZONDE MERGE
t[ 0x010249 ] = { COLOR_RARE, true } -- YELLOW MERGE
t[ 0x01024A ] = { COLOR_RARE, true } -- RECOVERY BARRIER
t[ 0x01024B ] = { COLOR_RARE, true } -- ASSIST  BARRIER
t[ 0x01024C ] = { COLOR_RARE, true } -- RED BARRIER
t[ 0x01024D ] = { COLOR_RARE, true } -- BLUE BARRIER
t[ 0x01024E ] = { COLOR_RARE, true } -- YELLOW BARRIER
t[ 0x01024F ] = { COLOR_RARE, true } -- WEAPONS GOLD SHIELD
t[ 0x010250 ] = { COLOR_RARE, true } -- BLACK GEAR
t[ 0x010251 ] = { COLOR_RARE, true } -- WORKS GUARD
t[ 0x010252 ] = { COLOR_RARE, true } -- RAGOL RING
t[ 0x010253 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010254 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010255 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010256 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010257 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010258 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x010259 ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x01025A ] = { COLOR_RARE, true } -- BLUE RING
t[ 0x01025B ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x01025C ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x01025D ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x01025E ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x01025F ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x010260 ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x010261 ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x010262 ] = { COLOR_RARE, true } -- GREEN RING
t[ 0x010263 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010264 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010265 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010266 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010267 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010268 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x010269 ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x01026A ] = { COLOR_RARE, true } -- YELLOW RING
t[ 0x01026B ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x01026C ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x01026D ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x01026E ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x01026F ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x010270 ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x010271 ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x010272 ] = { COLOR_RARE, true } -- PURPLE RING
t[ 0x010273 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010274 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010275 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010276 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010277 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010278 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x010279 ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x01027A ] = { COLOR_RARE, true } -- WHITE RING
t[ 0x01027B ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x01027C ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x01027D ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x01027E ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x01027F ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x010280 ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x010281 ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x010282 ] = { COLOR_RARE, true } -- BLACK RING
t[ 0x010283 ] = { COLOR_RARE, true } -- WEAPONS SILVER SHIELD
t[ 0x010284 ] = { COLOR_RARE, true } -- WEAPONS COPPER SHIELD
t[ 0x010285 ] = { COLOR_RARE, true } -- GRATIA
t[ 0x010286 ] = { COLOR_RARE, true } -- TRIPOLIC REFLECTOR
t[ 0x010287 ] = { COLOR_RARE, true } -- STRIKER PLUS
t[ 0x010288 ] = { COLOR_RARE, true } -- REGENERATE GEAR B.P.
t[ 0x010289 ] = { COLOR_RARE, true } -- RUPIKA
t[ 0x01028A ] = { COLOR_RARE, true } -- YATA MIRROR
t[ 0x01028B ] = { COLOR_RARE, true } -- BUNNY EARS
t[ 0x01028C ] = { COLOR_RARE, true } -- CAT EARS
t[ 0x01028D ] = { COLOR_RARE, true } -- THREE SEALS
t[ 0x01028E ] = { COLOR_RARE, true } -- GODS SHIELD "KOURYU"
t[ 0x01028F ] = { COLOR_RARE, true } -- DF SHIELD
t[ 0x010290 ] = { COLOR_RARE, true } -- FROM THE DEPTHS
t[ 0x010291 ] = { COLOR_RARE, true } -- DE ROL LE SHIELD
t[ 0x010292 ] = { COLOR_RARE, true } -- HONEYCOMB REFLECTOR
t[ 0x010293 ] = { COLOR_RARE, true } -- EPSIGUARD
t[ 0x010294 ] = { COLOR_RARE, true } -- ANGEL RING
t[ 0x010295 ] = { COLOR_RARE, true } -- UNION GUARD
t[ 0x010296 ] = { COLOR_RARE, true } -- UNION GUARD
t[ 0x010297 ] = { COLOR_RARE, true } -- UNION GUARD
t[ 0x010298 ] = { COLOR_RARE, true } -- UNION GUARD
t[ 0x010299 ] = { COLOR_RARE, true } -- STINK SHIELD
t[ 0x01029A ] = { COLOR_RARE, true } -- UNKNOWN_B
t[ 0x01029B ] = { COLOR_RARE, true } -- GENPEI
t[ 0x01029C ] = { COLOR_RARE, true } -- GENPEI
t[ 0x01029D ] = { COLOR_RARE, true } -- GENPEI
t[ 0x01029E ] = { COLOR_RARE, true } -- GENPEI
t[ 0x01029F ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A0 ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A1 ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A2 ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A3 ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A4 ] = { COLOR_RARE, true } -- GENPEI
t[ 0x0102A5 ] = { COLOR_DFLT, true } -- ????
---------------- Unit
t[ 0x010300 ] = { COLOR_DFLT, true } -- Knight/Power
t[ 0x010301 ] = { COLOR_DFLT, true } -- General/Power
t[ 0x010302 ] = { COLOR_DFLT, true } -- Ogre/Power
t[ 0x010303 ] = { COLOR_RARE, true } -- God/Power
t[ 0x010304 ] = { COLOR_DFLT, true } -- Priest/Mind
t[ 0x010305 ] = { COLOR_DFLT, true } -- General/Mind
t[ 0x010306 ] = { COLOR_DFLT, true } -- Angel/Mind
t[ 0x010307 ] = { COLOR_RARE, true } -- God/Mind
t[ 0x010308 ] = { COLOR_DFLT, true } -- Marksman/Arm
t[ 0x010309 ] = { COLOR_DFLT, true } -- General/Arm
t[ 0x01030A ] = { COLOR_DFLT, true } -- Elf/Arm
t[ 0x01030B ] = { COLOR_RARE, true } -- God/Arm
t[ 0x01030C ] = { COLOR_DFLT, true } -- Thief/Legs
t[ 0x01030D ] = { COLOR_DFLT, true } -- General/Legs
t[ 0x01030E ] = { COLOR_DFLT, true } -- Elf/Legs
t[ 0x01030F ] = { COLOR_RARE, true } -- God/Legs
t[ 0x010310 ] = { COLOR_DFLT, true } -- Digger/HP
t[ 0x010311 ] = { COLOR_DFLT, true } -- General/HP
t[ 0x010312 ] = { COLOR_DFLT, true } -- Dragon/HP
t[ 0x010313 ] = { COLOR_RARE, true } -- God/HP
t[ 0x010314 ] = { COLOR_DFLT, true } -- Magician/TP
t[ 0x010315 ] = { COLOR_DFLT, true } -- General/TP
t[ 0x010316 ] = { COLOR_DFLT, true } -- Angel/TP
t[ 0x010317 ] = { COLOR_RARE, true } -- God/TP
t[ 0x010318 ] = { COLOR_DFLT, true } -- Warrior/Body
t[ 0x010319 ] = { COLOR_DFLT, true } -- General/Body
t[ 0x01031A ] = { COLOR_DFLT, true } -- Metal/Body
t[ 0x01031B ] = { COLOR_RARE, true } -- God/Body
t[ 0x01031C ] = { COLOR_DFLT, true } -- Angel/Luck
t[ 0x01031D ] = { COLOR_RARE, true } -- God/Luck
t[ 0x01031E ] = { COLOR_DFLT, true } -- Master/Ability
t[ 0x01031F ] = { COLOR_RARE, true } -- Hero/Ability
t[ 0x010320 ] = { COLOR_RARE, true } -- God/Ability
t[ 0x010321 ] = { COLOR_DFLT, true } -- Resist/Fire
t[ 0x010322 ] = { COLOR_DFLT, true } -- Resist/Flame
t[ 0x010323 ] = { COLOR_DFLT, true } -- Resist/Burning
t[ 0x010324 ] = { COLOR_DFLT, true } -- Resist/Cold
t[ 0x010325 ] = { COLOR_DFLT, true } -- Resist/Freeze
t[ 0x010326 ] = { COLOR_DFLT, true } -- Resist/Blizzard
t[ 0x010327 ] = { COLOR_DFLT, true } -- Resist/Shock
t[ 0x010328 ] = { COLOR_DFLT, true } -- Resist/Thunder
t[ 0x010329 ] = { COLOR_DFLT, true } -- Resist/Storm
t[ 0x01032A ] = { COLOR_DFLT, true } -- Resist/Light
t[ 0x01032B ] = { COLOR_DFLT, true } -- Resist/Saint
t[ 0x01032C ] = { COLOR_DFLT, true } -- Resist/Holy
t[ 0x01032D ] = { COLOR_DFLT, true } -- Resist/Dark
t[ 0x01032E ] = { COLOR_DFLT, true } -- Resist/Evil
t[ 0x01032F ] = { COLOR_DFLT, true } -- Resist/Devil
t[ 0x010330 ] = { COLOR_DFLT, true } -- All/Resist
t[ 0x010331 ] = { COLOR_DFLT, true } -- Super/Resist
t[ 0x010332 ] = { COLOR_RARE, true } -- Perfect/Resist
t[ 0x010333 ] = { COLOR_DFLT, true } -- HP/Restorate
t[ 0x010334 ] = { COLOR_DFLT, true } -- HP/Generate
t[ 0x010335 ] = { COLOR_DFLT, true } -- HP/Revival
t[ 0x010336 ] = { COLOR_DFLT, true } -- TP/Restorate
t[ 0x010337 ] = { COLOR_DFLT, true } -- TP/Generate
t[ 0x010338 ] = { COLOR_DFLT, true } -- TP/Revival
t[ 0x010339 ] = { COLOR_DFLT, true } -- PB/Amplifier
t[ 0x01033A ] = { COLOR_DFLT, true } -- PB/Generate
t[ 0x01033B ] = { COLOR_DFLT, true } -- PB/Create
t[ 0x01033C ] = { COLOR_DFLT, true } -- Wizard/Technique
t[ 0x01033D ] = { COLOR_DFLT, true } -- Devil/Technique
t[ 0x01033E ] = { COLOR_RARE, true } -- God/Technique
t[ 0x01033F ] = { COLOR_DFLT, true } -- General/Battle
t[ 0x010340 ] = { COLOR_RARE, true } -- Devil/Battle
t[ 0x010341 ] = { COLOR_RARE, true } -- God/Battle
t[ 0x010342 ] = { COLOR_RARE, true } -- Cure/Poison
t[ 0x010343 ] = { COLOR_RARE, true } -- Cure/Paralysis
t[ 0x010344 ] = { COLOR_RARE, true } -- Cure/Slow
t[ 0x010345 ] = { COLOR_RARE, true } -- Cure/Confuse
t[ 0x010346 ] = { COLOR_RARE, true } -- Cure/Freeze
t[ 0x010347 ] = { COLOR_RARE, true } -- Cure/Shock
t[ 0x010348 ] = { COLOR_RARE, true } -- YASAKANI MAGATAMA
t[ 0x010349 ] = { COLOR_RARE, true } -- V101
t[ 0x01034A ] = { COLOR_RARE, true } -- V501
t[ 0x01034B ] = { COLOR_RARE, true } -- V502
t[ 0x01034C ] = { COLOR_RARE, true } -- V801
t[ 0x01034D ] = { COLOR_RARE, true } -- LIMITER
t[ 0x01034E ] = { COLOR_RARE, true } -- ADEPT
t[ 0x01034F ] = { COLOR_RARE, true } -- SWORDSMAN LORE
t[ 0x010350 ] = { COLOR_RARE, true } -- PROOF OF SWORD-SAINT
t[ 0x010351 ] = { COLOR_RARE, true } -- SMARTLINK
t[ 0x010352 ] = { COLOR_RARE, true } -- DIVINE PROTECTION
t[ 0x010353 ] = { COLOR_RARE, true } -- Heavenly/Battle
t[ 0x010354 ] = { COLOR_RARE, true } -- Heavenly/Power
t[ 0x010355 ] = { COLOR_RARE, true } -- Heavenly/Mind
t[ 0x010356 ] = { COLOR_RARE, true } -- Heavenly/Arms
t[ 0x010357 ] = { COLOR_RARE, true } -- Heavenly/Legs
t[ 0x010358 ] = { COLOR_RARE, true } -- Heavenly/Body
t[ 0x010359 ] = { COLOR_RARE, true } -- Heavenly/Luck
t[ 0x01035A ] = { COLOR_RARE, true } -- Heavenly/Ability
t[ 0x01035B ] = { COLOR_RARE, true } -- Centurion/Ability
t[ 0x01035C ] = { COLOR_RARE, true } -- Friend Ring
t[ 0x01035D ] = { COLOR_RARE, true } -- Heavenly/HP
t[ 0x01035E ] = { COLOR_RARE, true } -- Heavenly/TP
t[ 0x01035F ] = { COLOR_RARE, true } -- Heavenly/Resist
t[ 0x010360 ] = { COLOR_RARE, true } -- Heavenly/Technique
t[ 0x010361 ] = { COLOR_RARE, true } -- HP/Ressurection
t[ 0x010362 ] = { COLOR_RARE, true } -- TP/Ressurection
t[ 0x010363 ] = { COLOR_RARE, true } -- PB/Increase
t[ 0x010364 ] = { COLOR_DFLT, true } -- ????
---------------- Mag
t[ 0x020000 ] = { COLOR_DFLT, true } -- Mag
t[ 0x020100 ] = { COLOR_DFLT, true } -- Varuna
t[ 0x020200 ] = { COLOR_DFLT, true } -- Mitra
t[ 0x020300 ] = { COLOR_DFLT, true } -- Surya
t[ 0x020400 ] = { COLOR_DFLT, true } -- Vayu
t[ 0x020500 ] = { COLOR_DFLT, true } -- Varaha
t[ 0x020600 ] = { COLOR_DFLT, true } -- Kama
t[ 0x020700 ] = { COLOR_DFLT, true } -- Ushasu
t[ 0x020800 ] = { COLOR_DFLT, true } -- Apsaras
t[ 0x020900 ] = { COLOR_DFLT, true } -- Kumara
t[ 0x020A00 ] = { COLOR_DFLT, true } -- Kaitabha
t[ 0x020B00 ] = { COLOR_DFLT, true } -- Tapas
t[ 0x020C00 ] = { COLOR_DFLT, true } -- Bhirava
t[ 0x020D00 ] = { COLOR_DFLT, true } -- Kalki
t[ 0x020E00 ] = { COLOR_DFLT, true } -- Rudra
t[ 0x020F00 ] = { COLOR_DFLT, true } -- Marutah
t[ 0x021000 ] = { COLOR_DFLT, true } -- Yaksa
t[ 0x021100 ] = { COLOR_DFLT, true } -- Sita
t[ 0x021200 ] = { COLOR_DFLT, true } -- Garuda
t[ 0x021300 ] = { COLOR_DFLT, true } -- Nandin
t[ 0x021400 ] = { COLOR_DFLT, true } -- Ashvinau
t[ 0x021500 ] = { COLOR_DFLT, true } -- Ribhava
t[ 0x021600 ] = { COLOR_DFLT, true } -- Soma
t[ 0x021700 ] = { COLOR_DFLT, true } -- Ila
t[ 0x021800 ] = { COLOR_DFLT, true } -- Durga
t[ 0x021900 ] = { COLOR_DFLT, true } -- Vritra
t[ 0x021A00 ] = { COLOR_DFLT, true } -- Namuci
t[ 0x021B00 ] = { COLOR_DFLT, true } -- Sumba
t[ 0x021C00 ] = { COLOR_DFLT, true } -- Naga
t[ 0x021D00 ] = { COLOR_DFLT, true } -- Pitri
t[ 0x021E00 ] = { COLOR_DFLT, true } -- Kabanda
t[ 0x021F00 ] = { COLOR_DFLT, true } -- Ravana
t[ 0x022000 ] = { COLOR_DFLT, true } -- Marica
t[ 0x022100 ] = { COLOR_DFLT, true } -- Soniti
t[ 0x022200 ] = { COLOR_DFLT, true } -- Preta
t[ 0x022300 ] = { COLOR_DFLT, true } -- Andhaka
t[ 0x022400 ] = { COLOR_DFLT, true } -- Bana
t[ 0x022500 ] = { COLOR_DFLT, true } -- Naraka
t[ 0x022600 ] = { COLOR_DFLT, true } -- Madhu
t[ 0x022700 ] = { COLOR_DFLT, true } -- Churel
t[ 0x022800 ] = { COLOR_RARE, true } -- ROBOCHAO
t[ 0x022900 ] = { COLOR_RARE, true } -- OPA-OPA
t[ 0x022A00 ] = { COLOR_RARE, true } -- PIAN
t[ 0x022B00 ] = { COLOR_RARE, true } -- CHAO
t[ 0x022C00 ] = { COLOR_RARE, true } -- CHU CHU
t[ 0x022D00 ] = { COLOR_RARE, true } -- KAPU KAPU
t[ 0x022E00 ] = { COLOR_RARE, true } -- ANGEL'S WING
t[ 0x022F00 ] = { COLOR_RARE, true } -- DEVIL'S WING
t[ 0x023000 ] = { COLOR_RARE, true } -- ELENOR
t[ 0x023100 ] = { COLOR_RARE, true } -- MARK3
t[ 0x023200 ] = { COLOR_RARE, true } -- MASTER SYSTEM
t[ 0x023300 ] = { COLOR_RARE, true } -- GENESIS
t[ 0x023400 ] = { COLOR_RARE, true } -- SEGA SATURN
t[ 0x023500 ] = { COLOR_RARE, true } -- DREAMCAST
t[ 0x023600 ] = { COLOR_RARE, true } -- HAMBURGER
t[ 0x023700 ] = { COLOR_RARE, true } -- PANZER'S TAIL
t[ 0x023800 ] = { COLOR_RARE, true } -- DEVIL'S TAIL
t[ 0x023900 ] = { COLOR_DFLT, true } -- Deva
t[ 0x023A00 ] = { COLOR_DFLT, true } -- Rati
t[ 0x023B00 ] = { COLOR_DFLT, true } -- Savitri
t[ 0x023C00 ] = { COLOR_DFLT, true } -- Rukmin
t[ 0x023D00 ] = { COLOR_DFLT, true } -- Pushan
t[ 0x023E00 ] = { COLOR_DFLT, true } -- Diwari
t[ 0x023F00 ] = { COLOR_DFLT, true } -- Sato
t[ 0x024000 ] = { COLOR_DFLT, true } -- Bhima
t[ 0x024100 ] = { COLOR_DFLT, true } -- Nidra
t[ 0x024200 ] = { COLOR_DFLT, true } -- Geung-si
t[ 0x024300 ] = { COLOR_DFLT, true } -- \n
t[ 0x024400 ] = { COLOR_RARE, true } -- Tellusis
t[ 0x024500 ] = { COLOR_RARE, true } -- Striker Unit
t[ 0x024600 ] = { COLOR_RARE, true } -- Pioneer
t[ 0x024700 ] = { COLOR_RARE, true } -- Puyo
t[ 0x024800 ] = { COLOR_RARE, true } -- Moro
t[ 0x024900 ] = { COLOR_RARE, true } -- Rappy
t[ 0x024A00 ] = { COLOR_RARE, true } -- Yahoo!
t[ 0x024B00 ] = { COLOR_RARE, true } -- Gael Giel
t[ 0x024C00 ] = { COLOR_RARE, true } -- Agastya
t[ 0x024D00 ] = { COLOR_RARE, true } -- Cell of MAG 0503
t[ 0x024E00 ] = { COLOR_RARE, true } -- Cell of MAG 0504
t[ 0x024F00 ] = { COLOR_RARE, true } -- Cell of MAG 0505
t[ 0x025000 ] = { COLOR_RARE, true } -- Cell of MAG 0506
t[ 0x025100 ] = { COLOR_RARE, true } -- Cell of MAG 0507
t[ 0x025200 ] = { COLOR_DFLT, true } -- ????
---------------- Tool
t[ 0x030000 ] = { COLOR_TOOLS, true } -- Monomate
t[ 0x030001 ] = { COLOR_TOOLS, true } -- Dimate
t[ 0x030002 ] = { COLOR_TOOLS, true } -- Trimate
t[ 0x030100 ] = { COLOR_TOOLS, true } -- Monofluid
t[ 0x030101 ] = { COLOR_TOOLS, true } -- Difluid
t[ 0x030102 ] = { COLOR_TOOLS, true } -- Trifluid
-- Techniques at the end
t[ 0x030300 ] = { COLOR_TOOLS, true } -- Sol Atomizer
t[ 0x030400 ] = { COLOR_TOOLS, true } -- Moon Atomizer
t[ 0x030500 ] = { COLOR_TOOLS, true } -- Star Atomizer
t[ 0x030600 ] = { COLOR_TOOLS, true } -- Antidote
t[ 0x030601 ] = { COLOR_TOOLS, true } -- Antiparalysis
t[ 0x030700 ] = { COLOR_TOOLS, true } -- Telepipe
t[ 0x030800 ] = { COLOR_TOOLS, true } -- Trap Vision
t[ 0x030900 ] = { COLOR_SCAPEDOLL, true } -- Scape Doll
t[ 0x030A00 ] = { COLOR_GRINDERS, true } -- Monogrinder
t[ 0x030A01 ] = { COLOR_GRINDERS, true } -- Digrinder
t[ 0x030A02 ] = { COLOR_GRINDERS, true } -- Trigrinder
t[ 0x030B00 ] = { COLOR_MATS, true } -- Power Material
t[ 0x030B01 ] = { COLOR_MATS, true } -- Mind Material
t[ 0x030B02 ] = { COLOR_DFLT, true } -- Evade Material
t[ 0x030B03 ] = { COLOR_MATS, true } -- HP Material
t[ 0x030B04 ] = { COLOR_MATS, true } -- TP Material
t[ 0x030B05 ] = { COLOR_DFLT, true } -- Def Material
t[ 0x030B06 ] = { COLOR_RARE, true } -- Luck Material
t[ 0x030C00 ] = { COLOR_RARE, true } -- Cell of MAG 502
t[ 0x030C01 ] = { COLOR_RARE, true } -- Cell of MAG 213
t[ 0x030C02 ] = { COLOR_RARE, true } -- Parts of RoboChao
t[ 0x030C03 ] = { COLOR_RARE, true } -- Heart of Opa Opa
t[ 0x030C04 ] = { COLOR_RARE, true } -- Heart of Pian
t[ 0x030C05 ] = { COLOR_RARE, true } -- Heart of Chao
t[ 0x030D00 ] = { COLOR_RARE, true } -- Sorcerer's Right Arm
t[ 0x030D01 ] = { COLOR_RARE, true } -- S-beat's Arms
t[ 0x030D02 ] = { COLOR_RARE, true } -- P-arm's Arms
t[ 0x030D03 ] = { COLOR_RARE, true } -- Delsaber's Right Arm
t[ 0x030D04 ] = { COLOR_RARE, true } -- Bringer's Right Arm
t[ 0x030D05 ] = { COLOR_RARE, true } -- Delsaber's Left Arm
t[ 0x030D06 ] = { COLOR_RARE, true } -- S-red's Arms
t[ 0x030D07 ] = { COLOR_RARE, true } -- Dragon's Claw
t[ 0x030D08 ] = { COLOR_RARE, true } -- Hildebear's Head
t[ 0x030D09 ] = { COLOR_RARE, true } -- Hildeblue's Head
t[ 0x030D0A ] = { COLOR_RARE, true } -- Parts of Baranz
t[ 0x030D0B ] = { COLOR_RARE, true } -- Belra's Right Arm
t[ 0x030D0C ] = { COLOR_RARE, true } -- Gi Gue's body
t[ 0x030D0D ] = { COLOR_RARE, true } -- Sinow Berill's Arms
t[ 0x030D0E ] = { COLOR_RARE, true } -- Grass Assassin's Arms
t[ 0x030D0F ] = { COLOR_RARE, true } -- Booma's Right Arm
t[ 0x030D10 ] = { COLOR_RARE, true } -- Gobooma's Right Arm
t[ 0x030D11 ] = { COLOR_RARE, true } -- Gigobooma's Right Arm
t[ 0x030D12 ] = { COLOR_RARE, true } -- Gal Gryphon's Wing
t[ 0x030D13 ] = { COLOR_RARE, true } -- Rappy's Wing
t[ 0x030D14 ] = { COLOR_RARE, true } -- Cladding of Epsilon
t[ 0x030D15 ] = { COLOR_RARE, true } -- De Rol Le Shell
t[ 0x030E00 ] = { COLOR_RARE, true } -- Berill Photon
t[ 0x030E01 ] = { COLOR_RARE, true } -- Parasitic gene "Flow"
t[ 0x030E02 ] = { COLOR_RARE, true } -- Magic Stone "Iritista"
t[ 0x030E03 ] = { COLOR_RARE, true } -- Blue-black stone
t[ 0x030E04 ] = { COLOR_RARE, true } -- Syncesta
t[ 0x030E05 ] = { COLOR_RARE, true } -- Magic Water
t[ 0x030E06 ] = { COLOR_RARE, true } -- Parasitic cell Type D
t[ 0x030E07 ] = { COLOR_RARE, true } -- magic rock "Heart Key"
t[ 0x030E08 ] = { COLOR_RARE, true } -- magic rock "Moola"
t[ 0x030E09 ] = { COLOR_RARE, true } -- Star Amplifier
t[ 0x030E0A ] = { COLOR_RARE, true } -- Book of HITOGATA
t[ 0x030E0B ] = { COLOR_RARE, true } -- Heart of Chu Chu
t[ 0x030E0C ] = { COLOR_RARE, true } -- Parts of EGG BLASTER
t[ 0x030E0D ] = { COLOR_RARE, true } -- Heart of Angel
t[ 0x030E0E ] = { COLOR_RARE, true } -- Heart of Devil
t[ 0x030E0F ] = { COLOR_RARE, true } -- Kit of Hamburger
t[ 0x030E10 ] = { COLOR_RARE, true } -- Panther's Spirit
t[ 0x030E11 ] = { COLOR_RARE, true } -- Kit of MARK3
t[ 0x030E12 ] = { COLOR_RARE, true } -- Kit of MASTER SYSTEM
t[ 0x030E13 ] = { COLOR_RARE, true } -- Kit of GENESIS
t[ 0x030E14 ] = { COLOR_RARE, true } -- Kit of SEGA SATURN
t[ 0x030E15 ] = { COLOR_RARE, true } -- Kit of DREAMCAST
t[ 0x030E16 ] = { COLOR_RARE, true } -- Amplifier of Resta
t[ 0x030E17 ] = { COLOR_RARE, true } -- Amplifier of Anti
t[ 0x030E18 ] = { COLOR_RARE, true } -- Amplifier of Shifta
t[ 0x030E19 ] = { COLOR_RARE, true } -- Amplifier of Deband
t[ 0x030E1A ] = { COLOR_RARE, true } -- Amplifier of Foie
t[ 0x030E1B ] = { COLOR_RARE, true } -- Amplifier of Gifoie
t[ 0x030E1C ] = { COLOR_RARE, true } -- Amplifier of Rafoie
t[ 0x030E1D ] = { COLOR_RARE, true } -- Amplifier of Barta
t[ 0x030E1E ] = { COLOR_RARE, true } -- Amplifier of Gibarta
t[ 0x030E1F ] = { COLOR_RARE, true } -- Amplifier of Rabarta
t[ 0x030E20 ] = { COLOR_RARE, true } -- Amplifier of Zonde
t[ 0x030E21 ] = { COLOR_RARE, true } -- Amplifier of Gizonde
t[ 0x030E22 ] = { COLOR_RARE, true } -- Amplifier of Razonde
t[ 0x030E23 ] = { COLOR_RARE, true } -- Amplifier of Red
t[ 0x030E24 ] = { COLOR_RARE, true } -- Amplifier of Blue
t[ 0x030E25 ] = { COLOR_RARE, true } -- Amplifier of Yellow
t[ 0x030E26 ] = { COLOR_RARE, true } -- Heart of KAPU KAPU
t[ 0x030E27 ] = { COLOR_RARE, true } -- Photon Booster
t[ 0x030F00 ] = { COLOR_RARE, true } -- AddSlot
t[ 0x031000 ] = { COLOR_RARE, true } -- Photon Drop
t[ 0x031001 ] = { COLOR_RARE, true } -- Photon Sphere
t[ 0x031002 ] = { COLOR_RARE, true } -- Photon Crystal
t[ 0x031003 ] = { COLOR_RARE, true } -- Secret Ticket
t[ 0x031004 ] = { COLOR_RARE, true } -- Photon Ticket
t[ 0x031100 ] = { COLOR_RARE, true } -- Book of KATANA1
t[ 0x031101 ] = { COLOR_RARE, true } -- Book of KATANA2
t[ 0x031102 ] = { COLOR_RARE, true } -- Book of KATANA3
t[ 0x031200 ] = { COLOR_RARE, true } -- Weapons Bronze Badge
t[ 0x031201 ] = { COLOR_RARE, true } -- Weapons Silver Badge
t[ 0x031202 ] = { COLOR_RARE, true } -- Weapons Gold Badge
t[ 0x031203 ] = { COLOR_RARE, true } -- Weapons Crystal Badge
t[ 0x031204 ] = { COLOR_RARE, true } -- Weapons Steel Badge
t[ 0x031205 ] = { COLOR_RARE, true } -- Weapons Aluminum Badge
t[ 0x031206 ] = { COLOR_RARE, true } -- Weapons Leather Badge
t[ 0x031207 ] = { COLOR_RARE, true } -- Weapons Bone Badge
t[ 0x031208 ] = { COLOR_RARE, true } -- Letter of appreciation
t[ 0x031209 ] = { COLOR_RARE, true } -- Item Ticket
t[ 0x03120A ] = { COLOR_RARE, true } -- Valentine's Chocolate
t[ 0x03120B ] = { COLOR_RARE, true } -- New Year's Card
t[ 0x03120C ] = { COLOR_RARE, true } -- Christmas Card
t[ 0x03120D ] = { COLOR_RARE, true } -- Birthday Card
t[ 0x03120E ] = { COLOR_RARE, true } -- Proof of Sonic Team
t[ 0x03120F ] = { COLOR_RARE, true } -- Special Event Ticket
t[ 0x031210 ] = { COLOR_RARE, true } -- Flower Bouquet
t[ 0x031211 ] = { COLOR_RARE, true } -- Cake
t[ 0x031212 ] = { COLOR_RARE, true } -- Accessories
t[ 0x031213 ] = { COLOR_RARE, true } -- Mr.Naka's Business Card
t[ 0x031300 ] = { COLOR_RARE, true } -- Present
t[ 0x031400 ] = { COLOR_RARE, true } -- Chocolate
t[ 0x031401 ] = { COLOR_RARE, true } -- Candy
t[ 0x031402 ] = { COLOR_RARE, true } -- Cake
t[ 0x031403 ] = { COLOR_RARE, true } -- Weapons Silver Badge
t[ 0x031404 ] = { COLOR_RARE, true } -- Weapons Gold Badge
t[ 0x031405 ] = { COLOR_RARE, true } -- Weapons Crystal Badge
t[ 0x031406 ] = { COLOR_RARE, true } -- Weapons Steel Badge
t[ 0x031407 ] = { COLOR_RARE, true } -- Weapons Aluminum Badge
t[ 0x031408 ] = { COLOR_RARE, true } -- Weapons Leather Badge
t[ 0x031409 ] = { COLOR_RARE, true } -- Weapons Bone Badge
t[ 0x03140A ] = { COLOR_RARE, true } -- Bouquet
t[ 0x03140B ] = { COLOR_RARE, true } -- Decoction
t[ 0x031500 ] = { COLOR_RARE, true } -- Christmas Present
t[ 0x031501 ] = { COLOR_RARE, true } -- Easter Egg
t[ 0x031502 ] = { COLOR_RARE, true } -- Jack-O'-Lantern
t[ 0x031600 ] = { COLOR_MUSIC, true } -- DISK Vol.1 "Wedding March"
t[ 0x031601 ] = { COLOR_MUSIC, true } -- DISK Vol.2 "Day Light"
t[ 0x031602 ] = { COLOR_MUSIC, true } -- DISK Vol.3 "Burning Rangers"
t[ 0x031603 ] = { COLOR_MUSIC, true } -- DISK Vol.4 "Open Your Heart"
t[ 0x031604 ] = { COLOR_MUSIC, true } -- DISK Vol.5 "Live & Learn"
t[ 0x031605 ] = { COLOR_MUSIC, true } -- DISK Vol.6 "NiGHTS"
t[ 0x031606 ] = { COLOR_MUSIC, true } -- DISK Vol.7 "Ending Theme (Piano ver.)"
t[ 0x031607 ] = { COLOR_MUSIC, true } -- DISK Vol.8 "Heart to Heart"
t[ 0x031608 ] = { COLOR_MUSIC, true } -- DISK Vol.9 "Strange Blue"
t[ 0x031609 ] = { COLOR_MUSIC, true } -- DISK Vol.10 "Reunion System"
t[ 0x03160A ] = { COLOR_MUSIC, true } -- DISK Vol.11 "Pinnacles"
t[ 0x03160B ] = { COLOR_MUSIC, true } -- DISK Vol.12 "Fight inside the Spaceship"
t[ 0x031700 ] = { COLOR_RARE, true } -- Hunters Report
t[ 0x031701 ] = { COLOR_RARE, true } -- Hunters Report
t[ 0x031702 ] = { COLOR_RARE, true } -- Hunters Report
t[ 0x031703 ] = { COLOR_RARE, true } -- Hunters Report
t[ 0x031704 ] = { COLOR_RARE, true } -- Hunters Report
t[ 0x031800 ] = { COLOR_RARE, true } -- Tablet
t[ 0x031801 ] = { COLOR_RARE, true } -- UNKNOWN2
t[ 0x031802 ] = { COLOR_RARE, true } -- Dragon Scale
t[ 0x031803 ] = { COLOR_RARE, true } -- Heaven Striker Coat
t[ 0x031804 ] = { COLOR_RARE, true } -- Pioneer Parts
t[ 0x031805 ] = { COLOR_RARE, true } -- Amitie's Memo
t[ 0x031806 ] = { COLOR_RARE, true } -- Heart of Morolian
t[ 0x031807 ] = { COLOR_RARE, true } -- Rappy's Beak
t[ 0x031808 ] = { COLOR_RARE, true } -- Yahoo!'s engine
t[ 0x031809 ] = { COLOR_RARE, true } -- D-Photon Core
t[ 0x03180A ] = { COLOR_RARE, true } -- Liberta Kit
t[ 0x03180B ] = { COLOR_RARE, true } -- Cell of MAG 0503
t[ 0x03180C ] = { COLOR_RARE, true } -- Cell of MAG 0504
t[ 0x03180D ] = { COLOR_RARE, true } -- Cell of MAG 0505
t[ 0x03180E ] = { COLOR_RARE, true } -- Cell of MAG 0506
t[ 0x03180F ] = { COLOR_RARE, true } -- Cell of MAG 0507
t[ 0x031900 ] = { COLOR_RARE, true } -- Team Points 500
t[ 0x031901 ] = { COLOR_RARE, true } -- Team Points 1000
t[ 0x031902 ] = { COLOR_RARE, true } -- Team Points 5000
t[ 0x031903 ] = { COLOR_RARE, true } -- Team Points 10000
t[ 0x031A00 ] = { COLOR_DFLT, true } -- ????
-- MESETA
t[ 0x040000 ] = { COLOR_DFLT, true } -- Meseta
-- TECHNIQUES
t[ 0x050000 ] = { COLOR_DFLT, true } -- Foie Lv1
t[ 0x050001 ] = { COLOR_DFLT, true } -- Foie Lv2
t[ 0x050002 ] = { COLOR_DFLT, true } -- Foie Lv3
t[ 0x050003 ] = { COLOR_DFLT, true } -- Foie Lv4
t[ 0x050004 ] = { COLOR_DFLT, true } -- Foie Lv5
t[ 0x050005 ] = { COLOR_DFLT, true } -- Foie Lv6
t[ 0x050006 ] = { COLOR_DFLT, true } -- Foie Lv7
t[ 0x050007 ] = { COLOR_DFLT, true } -- Foie Lv8
t[ 0x050008 ] = { COLOR_DFLT, true } -- Foie Lv9
t[ 0x050009 ] = { COLOR_DFLT, true } -- Foie Lv10
t[ 0x05000A ] = { COLOR_DFLT, true } -- Foie Lv11
t[ 0x05000B ] = { COLOR_DFLT, true } -- Foie Lv12
t[ 0x05000C ] = { COLOR_DFLT, true } -- Foie Lv13
t[ 0x05000D ] = { COLOR_DFLT, true } -- Foie Lv14
t[ 0x05000E ] = { COLOR_GOOD_TECHS, true } -- Foie Lv15
t[ 0x05000F ] = { COLOR_DFLT, true } -- Foie Lv16
t[ 0x050010 ] = { COLOR_DFLT, true } -- Foie Lv17
t[ 0x050011 ] = { COLOR_DFLT, true } -- Foie Lv18
t[ 0x050012 ] = { COLOR_DFLT, true } -- Foie Lv19
t[ 0x050013 ] = { COLOR_GOOD_TECHS, true } -- Foie Lv20
t[ 0x050014 ] = { COLOR_DFLT, true } -- Foie Lv21
t[ 0x050015 ] = { COLOR_DFLT, true } -- Foie Lv22
t[ 0x050016 ] = { COLOR_DFLT, true } -- Foie Lv23
t[ 0x050017 ] = { COLOR_DFLT, true } -- Foie Lv24
t[ 0x050018 ] = { COLOR_DFLT, true } -- Foie Lv25
t[ 0x050019 ] = { COLOR_DFLT, true } -- Foie Lv26
t[ 0x05001A ] = { COLOR_DFLT, true } -- Foie Lv27
t[ 0x05001B ] = { COLOR_DFLT, true } -- Foie Lv28
t[ 0x05001C ] = { COLOR_GOOD_TECHS, true } -- Foie Lv29
t[ 0x05001D ] = { COLOR_RARE, true } -- Foie Lv30
t[ 0x050100 ] = { COLOR_DFLT, true } -- Gifoie Lv1
t[ 0x050101 ] = { COLOR_DFLT, true } -- Gifoie Lv2
t[ 0x050102 ] = { COLOR_DFLT, true } -- Gifoie Lv3
t[ 0x050103 ] = { COLOR_DFLT, true } -- Gifoie Lv4
t[ 0x050104 ] = { COLOR_DFLT, true } -- Gifoie Lv5
t[ 0x050105 ] = { COLOR_DFLT, true } -- Gifoie Lv6
t[ 0x050106 ] = { COLOR_DFLT, true } -- Gifoie Lv7
t[ 0x050107 ] = { COLOR_DFLT, true } -- Gifoie Lv8
t[ 0x050108 ] = { COLOR_DFLT, true } -- Gifoie Lv9
t[ 0x050109 ] = { COLOR_DFLT, true } -- Gifoie Lv10
t[ 0x05010A ] = { COLOR_DFLT, true } -- Gifoie Lv11
t[ 0x05010B ] = { COLOR_DFLT, true } -- Gifoie Lv12
t[ 0x05010C ] = { COLOR_DFLT, true } -- Gifoie Lv13
t[ 0x05010D ] = { COLOR_DFLT, true } -- Gifoie Lv14
t[ 0x05010E ] = { COLOR_GOOD_TECHS, true } -- Gifoie Lv15
t[ 0x05010F ] = { COLOR_DFLT, true } -- Gifoie Lv16
t[ 0x050110 ] = { COLOR_DFLT, true } -- Gifoie Lv17
t[ 0x050111 ] = { COLOR_DFLT, true } -- Gifoie Lv18
t[ 0x050112 ] = { COLOR_DFLT, true } -- Gifoie Lv19
t[ 0x050113 ] = { COLOR_GOOD_TECHS, true } -- Gifoie Lv20
t[ 0x050114 ] = { COLOR_DFLT, true } -- Gifoie Lv21
t[ 0x050115 ] = { COLOR_DFLT, true } -- Gifoie Lv22
t[ 0x050116 ] = { COLOR_DFLT, true } -- Gifoie Lv23
t[ 0x050117 ] = { COLOR_DFLT, true } -- Gifoie Lv24
t[ 0x050118 ] = { COLOR_DFLT, true } -- Gifoie Lv25
t[ 0x050119 ] = { COLOR_DFLT, true } -- Gifoie Lv26
t[ 0x05011A ] = { COLOR_DFLT, true } -- Gifoie Lv27
t[ 0x05011B ] = { COLOR_DFLT, true } -- Gifoie Lv28
t[ 0x05011C ] = { COLOR_GOOD_TECHS, true } -- Gifoie Lv29
t[ 0x05011D ] = { COLOR_RARE, true } -- Gifoie Lv30
t[ 0x050200 ] = { COLOR_DFLT, true } -- Rafoie Lv1
t[ 0x050201 ] = { COLOR_DFLT, true } -- Rafoie Lv2
t[ 0x050202 ] = { COLOR_DFLT, true } -- Rafoie Lv3
t[ 0x050203 ] = { COLOR_DFLT, true } -- Rafoie Lv4
t[ 0x050204 ] = { COLOR_DFLT, true } -- Rafoie Lv5
t[ 0x050205 ] = { COLOR_DFLT, true } -- Rafoie Lv6
t[ 0x050206 ] = { COLOR_DFLT, true } -- Rafoie Lv7
t[ 0x050207 ] = { COLOR_DFLT, true } -- Rafoie Lv8
t[ 0x050208 ] = { COLOR_DFLT, true } -- Rafoie Lv9
t[ 0x050209 ] = { COLOR_DFLT, true } -- Rafoie Lv10
t[ 0x05020A ] = { COLOR_DFLT, true } -- Rafoie Lv11
t[ 0x05020B ] = { COLOR_DFLT, true } -- Rafoie Lv12
t[ 0x05020C ] = { COLOR_DFLT, true } -- Rafoie Lv13
t[ 0x05020D ] = { COLOR_DFLT, true } -- Rafoie Lv14
t[ 0x05020E ] = { COLOR_GOOD_TECHS, true } -- Rafoie Lv15
t[ 0x05020F ] = { COLOR_DFLT, true } -- Rafoie Lv16
t[ 0x050210 ] = { COLOR_DFLT, true } -- Rafoie Lv17
t[ 0x050211 ] = { COLOR_DFLT, true } -- Rafoie Lv18
t[ 0x050212 ] = { COLOR_DFLT, true } -- Rafoie Lv19
t[ 0x050213 ] = { COLOR_GOOD_TECHS, true } -- Rafoie Lv20
t[ 0x050214 ] = { COLOR_DFLT, true } -- Rafoie Lv21
t[ 0x050215 ] = { COLOR_DFLT, true } -- Rafoie Lv22
t[ 0x050216 ] = { COLOR_DFLT, true } -- Rafoie Lv23
t[ 0x050217 ] = { COLOR_DFLT, true } -- Rafoie Lv24
t[ 0x050218 ] = { COLOR_DFLT, true } -- Rafoie Lv25
t[ 0x050219 ] = { COLOR_DFLT, true } -- Rafoie Lv26
t[ 0x05021A ] = { COLOR_DFLT, true } -- Rafoie Lv27
t[ 0x05021B ] = { COLOR_DFLT, true } -- Rafoie Lv28
t[ 0x05021C ] = { COLOR_GOOD_TECHS, true } -- Rafoie Lv29
t[ 0x05021D ] = { COLOR_RARE, true } -- Rafoie Lv30
t[ 0x050300 ] = { COLOR_DFLT, true } -- Barta Lv1
t[ 0x050301 ] = { COLOR_DFLT, true } -- Barta Lv2
t[ 0x050302 ] = { COLOR_DFLT, true } -- Barta Lv3
t[ 0x050303 ] = { COLOR_DFLT, true } -- Barta Lv4
t[ 0x050304 ] = { COLOR_DFLT, true } -- Barta Lv5
t[ 0x050305 ] = { COLOR_DFLT, true } -- Barta Lv6
t[ 0x050306 ] = { COLOR_DFLT, true } -- Barta Lv7
t[ 0x050307 ] = { COLOR_DFLT, true } -- Barta Lv8
t[ 0x050308 ] = { COLOR_DFLT, true } -- Barta Lv9
t[ 0x050309 ] = { COLOR_DFLT, true } -- Barta Lv10
t[ 0x05030A ] = { COLOR_DFLT, true } -- Barta Lv11
t[ 0x05030B ] = { COLOR_DFLT, true } -- Barta Lv12
t[ 0x05030C ] = { COLOR_DFLT, true } -- Barta Lv13
t[ 0x05030D ] = { COLOR_DFLT, true } -- Barta Lv14
t[ 0x05030E ] = { COLOR_GOOD_TECHS, true } -- Barta Lv15
t[ 0x05030F ] = { COLOR_DFLT, true } -- Barta Lv16
t[ 0x050310 ] = { COLOR_DFLT, true } -- Barta Lv17
t[ 0x050311 ] = { COLOR_DFLT, true } -- Barta Lv18
t[ 0x050312 ] = { COLOR_DFLT, true } -- Barta Lv19
t[ 0x050313 ] = { COLOR_GOOD_TECHS, true } -- Barta Lv20
t[ 0x050314 ] = { COLOR_DFLT, true } -- Barta Lv21
t[ 0x050315 ] = { COLOR_DFLT, true } -- Barta Lv22
t[ 0x050316 ] = { COLOR_DFLT, true } -- Barta Lv23
t[ 0x050317 ] = { COLOR_DFLT, true } -- Barta Lv24
t[ 0x050318 ] = { COLOR_DFLT, true } -- Barta Lv25
t[ 0x050319 ] = { COLOR_DFLT, true } -- Barta Lv26
t[ 0x05031A ] = { COLOR_DFLT, true } -- Barta Lv27
t[ 0x05031B ] = { COLOR_DFLT, true } -- Barta Lv28
t[ 0x05031C ] = { COLOR_GOOD_TECHS, true } -- Barta Lv29
t[ 0x05031D ] = { COLOR_RARE, true } -- Barta Lv30
t[ 0x050400 ] = { COLOR_DFLT, true } -- Gibarta Lv1
t[ 0x050401 ] = { COLOR_DFLT, true } -- Gibarta Lv2
t[ 0x050402 ] = { COLOR_DFLT, true } -- Gibarta Lv3
t[ 0x050403 ] = { COLOR_DFLT, true } -- Gibarta Lv4
t[ 0x050404 ] = { COLOR_DFLT, true } -- Gibarta Lv5
t[ 0x050405 ] = { COLOR_DFLT, true } -- Gibarta Lv6
t[ 0x050406 ] = { COLOR_DFLT, true } -- Gibarta Lv7
t[ 0x050407 ] = { COLOR_DFLT, true } -- Gibarta Lv8
t[ 0x050408 ] = { COLOR_DFLT, true } -- Gibarta Lv9
t[ 0x050409 ] = { COLOR_DFLT, true } -- Gibarta Lv10
t[ 0x05040A ] = { COLOR_DFLT, true } -- Gibarta Lv11
t[ 0x05040B ] = { COLOR_DFLT, true } -- Gibarta Lv12
t[ 0x05040C ] = { COLOR_DFLT, true } -- Gibarta Lv13
t[ 0x05040D ] = { COLOR_DFLT, true } -- Gibarta Lv14
t[ 0x05040E ] = { COLOR_GOOD_TECHS, true } -- Gibarta Lv15
t[ 0x05040F ] = { COLOR_DFLT, true } -- Gibarta Lv16
t[ 0x050410 ] = { COLOR_DFLT, true } -- Gibarta Lv17
t[ 0x050411 ] = { COLOR_DFLT, true } -- Gibarta Lv18
t[ 0x050412 ] = { COLOR_DFLT, true } -- Gibarta Lv19
t[ 0x050413 ] = { COLOR_GOOD_TECHS, true } -- Gibarta Lv20
t[ 0x050414 ] = { COLOR_DFLT, true } -- Gibarta Lv21
t[ 0x050415 ] = { COLOR_DFLT, true } -- Gibarta Lv22
t[ 0x050416 ] = { COLOR_DFLT, true } -- Gibarta Lv23
t[ 0x050417 ] = { COLOR_DFLT, true } -- Gibarta Lv24
t[ 0x050418 ] = { COLOR_DFLT, true } -- Gibarta Lv25
t[ 0x050419 ] = { COLOR_DFLT, true } -- Gibarta Lv26
t[ 0x05041A ] = { COLOR_DFLT, true } -- Gibarta Lv27
t[ 0x05041B ] = { COLOR_DFLT, true } -- Gibarta Lv28
t[ 0x05041C ] = { COLOR_GOOD_TECHS, true } -- Gibarta Lv29
t[ 0x05041D ] = { COLOR_RARE, true } -- Gibarta Lv30
t[ 0x050500 ] = { COLOR_DFLT, true } -- Rabarta Lv1
t[ 0x050501 ] = { COLOR_DFLT, true } -- Rabarta Lv2
t[ 0x050502 ] = { COLOR_DFLT, true } -- Rabarta Lv3
t[ 0x050503 ] = { COLOR_DFLT, true } -- Rabarta Lv4
t[ 0x050504 ] = { COLOR_DFLT, true } -- Rabarta Lv5
t[ 0x050505 ] = { COLOR_DFLT, true } -- Rabarta Lv6
t[ 0x050506 ] = { COLOR_DFLT, true } -- Rabarta Lv7
t[ 0x050507 ] = { COLOR_DFLT, true } -- Rabarta Lv8
t[ 0x050508 ] = { COLOR_DFLT, true } -- Rabarta Lv9
t[ 0x050509 ] = { COLOR_DFLT, true } -- Rabarta Lv10
t[ 0x05050A ] = { COLOR_DFLT, true } -- Rabarta Lv11
t[ 0x05050B ] = { COLOR_DFLT, true } -- Rabarta Lv12
t[ 0x05050C ] = { COLOR_DFLT, true } -- Rabarta Lv13
t[ 0x05050D ] = { COLOR_DFLT, true } -- Rabarta Lv14
t[ 0x05050E ] = { COLOR_GOOD_TECHS, true } -- Rabarta Lv15
t[ 0x05050F ] = { COLOR_DFLT, true } -- Rabarta Lv16
t[ 0x050510 ] = { COLOR_DFLT, true } -- Rabarta Lv17
t[ 0x050511 ] = { COLOR_DFLT, true } -- Rabarta Lv18
t[ 0x050512 ] = { COLOR_DFLT, true } -- Rabarta Lv19
t[ 0x050513 ] = { COLOR_GOOD_TECHS, true } -- Rabarta Lv20
t[ 0x050514 ] = { COLOR_DFLT, true } -- Rabarta Lv21
t[ 0x050515 ] = { COLOR_DFLT, true } -- Rabarta Lv22
t[ 0x050516 ] = { COLOR_DFLT, true } -- Rabarta Lv23
t[ 0x050517 ] = { COLOR_DFLT, true } -- Rabarta Lv24
t[ 0x050518 ] = { COLOR_DFLT, true } -- Rabarta Lv25
t[ 0x050519 ] = { COLOR_DFLT, true } -- Rabarta Lv26
t[ 0x05051A ] = { COLOR_DFLT, true } -- Rabarta Lv27
t[ 0x05051B ] = { COLOR_DFLT, true } -- Rabarta Lv28
t[ 0x05051C ] = { COLOR_GOOD_TECHS, true } -- Rabarta Lv29
t[ 0x05051D ] = { COLOR_RARE, true } -- Rabarta Lv30
t[ 0x050600 ] = { COLOR_DFLT, true } -- Zonde Lv1
t[ 0x050601 ] = { COLOR_DFLT, true } -- Zonde Lv2
t[ 0x050602 ] = { COLOR_DFLT, true } -- Zonde Lv3
t[ 0x050603 ] = { COLOR_DFLT, true } -- Zonde Lv4
t[ 0x050604 ] = { COLOR_DFLT, true } -- Zonde Lv5
t[ 0x050605 ] = { COLOR_DFLT, true } -- Zonde Lv6
t[ 0x050606 ] = { COLOR_DFLT, true } -- Zonde Lv7
t[ 0x050607 ] = { COLOR_DFLT, true } -- Zonde Lv8
t[ 0x050608 ] = { COLOR_DFLT, true } -- Zonde Lv9
t[ 0x050609 ] = { COLOR_DFLT, true } -- Zonde Lv10
t[ 0x05060A ] = { COLOR_DFLT, true } -- Zonde Lv11
t[ 0x05060B ] = { COLOR_DFLT, true } -- Zonde Lv12
t[ 0x05060C ] = { COLOR_DFLT, true } -- Zonde Lv13
t[ 0x05060D ] = { COLOR_DFLT, true } -- Zonde Lv14
t[ 0x05060E ] = { COLOR_GOOD_TECHS, true } -- Zonde Lv15
t[ 0x05060F ] = { COLOR_DFLT, true } -- Zonde Lv16
t[ 0x050610 ] = { COLOR_DFLT, true } -- Zonde Lv17
t[ 0x050611 ] = { COLOR_DFLT, true } -- Zonde Lv18
t[ 0x050612 ] = { COLOR_DFLT, true } -- Zonde Lv19
t[ 0x050613 ] = { COLOR_GOOD_TECHS, true } -- Zonde Lv20
t[ 0x050614 ] = { COLOR_DFLT, true } -- Zonde Lv21
t[ 0x050615 ] = { COLOR_DFLT, true } -- Zonde Lv22
t[ 0x050616 ] = { COLOR_DFLT, true } -- Zonde Lv23
t[ 0x050617 ] = { COLOR_DFLT, true } -- Zonde Lv24
t[ 0x050618 ] = { COLOR_DFLT, true } -- Zonde Lv25
t[ 0x050619 ] = { COLOR_DFLT, true } -- Zonde Lv26
t[ 0x05061A ] = { COLOR_DFLT, true } -- Zonde Lv27
t[ 0x05061B ] = { COLOR_DFLT, true } -- Zonde Lv28
t[ 0x05061C ] = { COLOR_GOOD_TECHS, true } -- Zonde Lv29
t[ 0x05061D ] = { COLOR_RARE, true } -- Zonde Lv30
t[ 0x050700 ] = { COLOR_DFLT, true } -- Gizonde Lv1
t[ 0x050701 ] = { COLOR_DFLT, true } -- Gizonde Lv2
t[ 0x050702 ] = { COLOR_DFLT, true } -- Gizonde Lv3
t[ 0x050703 ] = { COLOR_DFLT, true } -- Gizonde Lv4
t[ 0x050704 ] = { COLOR_DFLT, true } -- Gizonde Lv5
t[ 0x050705 ] = { COLOR_DFLT, true } -- Gizonde Lv6
t[ 0x050706 ] = { COLOR_DFLT, true } -- Gizonde Lv7
t[ 0x050707 ] = { COLOR_DFLT, true } -- Gizonde Lv8
t[ 0x050708 ] = { COLOR_DFLT, true } -- Gizonde Lv9
t[ 0x050709 ] = { COLOR_DFLT, true } -- Gizonde Lv10
t[ 0x05070A ] = { COLOR_DFLT, true } -- Gizonde Lv11
t[ 0x05070B ] = { COLOR_DFLT, true } -- Gizonde Lv12
t[ 0x05070C ] = { COLOR_DFLT, true } -- Gizonde Lv13
t[ 0x05070D ] = { COLOR_DFLT, true } -- Gizonde Lv14
t[ 0x05070E ] = { COLOR_GOOD_TECHS, true } -- Gizonde Lv15
t[ 0x05070F ] = { COLOR_DFLT, true } -- Gizonde Lv16
t[ 0x050710 ] = { COLOR_DFLT, true } -- Gizonde Lv17
t[ 0x050711 ] = { COLOR_DFLT, true } -- Gizonde Lv18
t[ 0x050712 ] = { COLOR_DFLT, true } -- Gizonde Lv19
t[ 0x050713 ] = { COLOR_GOOD_TECHS, true } -- Gizonde Lv20
t[ 0x050714 ] = { COLOR_DFLT, true } -- Gizonde Lv21
t[ 0x050715 ] = { COLOR_DFLT, true } -- Gizonde Lv22
t[ 0x050716 ] = { COLOR_DFLT, true } -- Gizonde Lv23
t[ 0x050717 ] = { COLOR_DFLT, true } -- Gizonde Lv24
t[ 0x050718 ] = { COLOR_DFLT, true } -- Gizonde Lv25
t[ 0x050719 ] = { COLOR_DFLT, true } -- Gizonde Lv26
t[ 0x05071A ] = { COLOR_DFLT, true } -- Gizonde Lv27
t[ 0x05071B ] = { COLOR_DFLT, true } -- Gizonde Lv28
t[ 0x05071C ] = { COLOR_GOOD_TECHS, true } -- Gizonde Lv29
t[ 0x05071D ] = { COLOR_RARE, true } -- Gizonde Lv30
t[ 0x050800 ] = { COLOR_DFLT, true } -- Razonde Lv1
t[ 0x050801 ] = { COLOR_DFLT, true } -- Razonde Lv2
t[ 0x050802 ] = { COLOR_DFLT, true } -- Razonde Lv3
t[ 0x050803 ] = { COLOR_DFLT, true } -- Razonde Lv4
t[ 0x050804 ] = { COLOR_DFLT, true } -- Razonde Lv5
t[ 0x050805 ] = { COLOR_DFLT, true } -- Razonde Lv6
t[ 0x050806 ] = { COLOR_DFLT, true } -- Razonde Lv7
t[ 0x050807 ] = { COLOR_DFLT, true } -- Razonde Lv8
t[ 0x050808 ] = { COLOR_DFLT, true } -- Razonde Lv9
t[ 0x050809 ] = { COLOR_DFLT, true } -- Razonde Lv10
t[ 0x05080A ] = { COLOR_DFLT, true } -- Razonde Lv11
t[ 0x05080B ] = { COLOR_DFLT, true } -- Razonde Lv12
t[ 0x05080C ] = { COLOR_DFLT, true } -- Razonde Lv13
t[ 0x05080D ] = { COLOR_DFLT, true } -- Razonde Lv14
t[ 0x05080E ] = { COLOR_GOOD_TECHS, true } -- Razonde Lv15
t[ 0x05080F ] = { COLOR_DFLT, true } -- Razonde Lv16
t[ 0x050810 ] = { COLOR_DFLT, true } -- Razonde Lv17
t[ 0x050811 ] = { COLOR_DFLT, true } -- Razonde Lv18
t[ 0x050812 ] = { COLOR_DFLT, true } -- Razonde Lv19
t[ 0x050813 ] = { COLOR_GOOD_TECHS, true } -- Razonde Lv20
t[ 0x050814 ] = { COLOR_DFLT, true } -- Razonde Lv21
t[ 0x050815 ] = { COLOR_DFLT, true } -- Razonde Lv22
t[ 0x050816 ] = { COLOR_DFLT, true } -- Razonde Lv23
t[ 0x050817 ] = { COLOR_DFLT, true } -- Razonde Lv24
t[ 0x050818 ] = { COLOR_DFLT, true } -- Razonde Lv25
t[ 0x050819 ] = { COLOR_DFLT, true } -- Razonde Lv26
t[ 0x05081A ] = { COLOR_DFLT, true } -- Razonde Lv27
t[ 0x05081B ] = { COLOR_DFLT, true } -- Razonde Lv28
t[ 0x05081C ] = { COLOR_GOOD_TECHS, true } -- Razonde Lv29
t[ 0x05081D ] = { COLOR_RARE, true } -- Razonde Lv30
t[ 0x050900 ] = { COLOR_DFLT, true } -- Grants Lv1
t[ 0x050901 ] = { COLOR_DFLT, true } -- Grants Lv2
t[ 0x050902 ] = { COLOR_DFLT, true } -- Grants Lv3
t[ 0x050903 ] = { COLOR_DFLT, true } -- Grants Lv4
t[ 0x050904 ] = { COLOR_DFLT, true } -- Grants Lv5
t[ 0x050905 ] = { COLOR_DFLT, true } -- Grants Lv6
t[ 0x050906 ] = { COLOR_DFLT, true } -- Grants Lv7
t[ 0x050907 ] = { COLOR_DFLT, true } -- Grants Lv8
t[ 0x050908 ] = { COLOR_DFLT, true } -- Grants Lv9
t[ 0x050909 ] = { COLOR_DFLT, true } -- Grants Lv10
t[ 0x05090A ] = { COLOR_DFLT, true } -- Grants Lv11
t[ 0x05090B ] = { COLOR_DFLT, true } -- Grants Lv12
t[ 0x05090C ] = { COLOR_DFLT, true } -- Grants Lv13
t[ 0x05090D ] = { COLOR_DFLT, true } -- Grants Lv14
t[ 0x05090E ] = { COLOR_DFLT, true } -- Grants Lv15
t[ 0x05090F ] = { COLOR_DFLT, true } -- Grants Lv16
t[ 0x050910 ] = { COLOR_DFLT, true } -- Grants Lv17
t[ 0x050911 ] = { COLOR_DFLT, true } -- Grants Lv18
t[ 0x050912 ] = { COLOR_DFLT, true } -- Grants Lv19
t[ 0x050913 ] = { COLOR_DFLT, true } -- Grants Lv20
t[ 0x050914 ] = { COLOR_DFLT, true } -- Grants Lv21
t[ 0x050915 ] = { COLOR_DFLT, true } -- Grants Lv22
t[ 0x050916 ] = { COLOR_DFLT, true } -- Grants Lv23
t[ 0x050917 ] = { COLOR_DFLT, true } -- Grants Lv24
t[ 0x050918 ] = { COLOR_GOOD_TECHS, true } -- Grants Lv25
t[ 0x050919 ] = { COLOR_GOOD_TECHS, true } -- Grants Lv26
t[ 0x05091A ] = { COLOR_GOOD_TECHS, true } -- Grants Lv27
t[ 0x05091B ] = { COLOR_GOOD_TECHS, true } -- Grants Lv28
t[ 0x05091C ] = { COLOR_GOOD_TECHS, true } -- Grants Lv29
t[ 0x05091D ] = { COLOR_RARE, true } -- Grants Lv30
t[ 0x050A00 ] = { COLOR_DFLT, true } -- Deband Lv1
t[ 0x050A01 ] = { COLOR_DFLT, true } -- Deband Lv2
t[ 0x050A02 ] = { COLOR_DFLT, true } -- Deband Lv3
t[ 0x050A03 ] = { COLOR_DFLT, true } -- Deband Lv4
t[ 0x050A04 ] = { COLOR_DFLT, true } -- Deband Lv5
t[ 0x050A05 ] = { COLOR_DFLT, true } -- Deband Lv6
t[ 0x050A06 ] = { COLOR_DFLT, true } -- Deband Lv7
t[ 0x050A07 ] = { COLOR_DFLT, true } -- Deband Lv8
t[ 0x050A08 ] = { COLOR_DFLT, true } -- Deband Lv9
t[ 0x050A09 ] = { COLOR_DFLT, true } -- Deband Lv10
t[ 0x050A0A ] = { COLOR_DFLT, true } -- Deband Lv11
t[ 0x050A0B ] = { COLOR_DFLT, true } -- Deband Lv12
t[ 0x050A0C ] = { COLOR_DFLT, true } -- Deband Lv13
t[ 0x050A0D ] = { COLOR_DFLT, true } -- Deband Lv14
t[ 0x050A0E ] = { COLOR_GOOD_TECHS, true } -- Deband Lv15
t[ 0x050A0F ] = { COLOR_DFLT, true } -- Deband Lv16
t[ 0x050A10 ] = { COLOR_DFLT, true } -- Deband Lv17
t[ 0x050A11 ] = { COLOR_DFLT, true } -- Deband Lv18
t[ 0x050A12 ] = { COLOR_DFLT, true } -- Deband Lv19
t[ 0x050A13 ] = { COLOR_GOOD_TECHS, true } -- Deband Lv20
t[ 0x050A14 ] = { COLOR_DFLT, true } -- Deband Lv21
t[ 0x050A15 ] = { COLOR_DFLT, true } -- Deband Lv22
t[ 0x050A16 ] = { COLOR_DFLT, true } -- Deband Lv23
t[ 0x050A17 ] = { COLOR_DFLT, true } -- Deband Lv24
t[ 0x050A18 ] = { COLOR_DFLT, true } -- Deband Lv25
t[ 0x050A19 ] = { COLOR_DFLT, true } -- Deband Lv26
t[ 0x050A1A ] = { COLOR_DFLT, true } -- Deband Lv27
t[ 0x050A1B ] = { COLOR_DFLT, true } -- Deband Lv28
t[ 0x050A1C ] = { COLOR_DFLT, true } -- Deband Lv29
t[ 0x050A1D ] = { COLOR_GOOD_TECHS, true } -- Deband Lv30
t[ 0x050B00 ] = { COLOR_DFLT, true } -- Jellen Lv1
t[ 0x050B01 ] = { COLOR_DFLT, true } -- Jellen Lv2
t[ 0x050B02 ] = { COLOR_DFLT, true } -- Jellen Lv3
t[ 0x050B03 ] = { COLOR_DFLT, true } -- Jellen Lv4
t[ 0x050B04 ] = { COLOR_DFLT, true } -- Jellen Lv5
t[ 0x050B05 ] = { COLOR_DFLT, true } -- Jellen Lv6
t[ 0x050B06 ] = { COLOR_DFLT, true } -- Jellen Lv7
t[ 0x050B07 ] = { COLOR_DFLT, true } -- Jellen Lv8
t[ 0x050B08 ] = { COLOR_DFLT, true } -- Jellen Lv9
t[ 0x050B09 ] = { COLOR_DFLT, true } -- Jellen Lv10
t[ 0x050B0A ] = { COLOR_DFLT, true } -- Jellen Lv11
t[ 0x050B0B ] = { COLOR_DFLT, true } -- Jellen Lv12
t[ 0x050B0C ] = { COLOR_DFLT, true } -- Jellen Lv13
t[ 0x050B0D ] = { COLOR_DFLT, true } -- Jellen Lv14
t[ 0x050B0E ] = { COLOR_GOOD_TECHS, true } -- Jellen Lv15
t[ 0x050B0F ] = { COLOR_DFLT, true } -- Jellen Lv16
t[ 0x050B10 ] = { COLOR_DFLT, true } -- Jellen Lv17
t[ 0x050B11 ] = { COLOR_DFLT, true } -- Jellen Lv18
t[ 0x050B12 ] = { COLOR_DFLT, true } -- Jellen Lv19
t[ 0x050B13 ] = { COLOR_GOOD_TECHS, true } -- Jellen Lv20
t[ 0x050B14 ] = { COLOR_DFLT, true } -- Jellen Lv21
t[ 0x050B15 ] = { COLOR_DFLT, true } -- Jellen Lv22
t[ 0x050B16 ] = { COLOR_DFLT, true } -- Jellen Lv23
t[ 0x050B17 ] = { COLOR_DFLT, true } -- Jellen Lv24
t[ 0x050B18 ] = { COLOR_DFLT, true } -- Jellen Lv25
t[ 0x050B19 ] = { COLOR_DFLT, true } -- Jellen Lv26
t[ 0x050B1A ] = { COLOR_DFLT, true } -- Jellen Lv27
t[ 0x050B1B ] = { COLOR_DFLT, true } -- Jellen Lv28
t[ 0x050B1C ] = { COLOR_DFLT, true } -- Jellen Lv29
t[ 0x050B1D ] = { COLOR_GOOD_TECHS, true } -- Jellen Lv30
t[ 0x050C00 ] = { COLOR_DFLT, true } -- Zalure Lv1
t[ 0x050C01 ] = { COLOR_DFLT, true } -- Zalure Lv2
t[ 0x050C02 ] = { COLOR_DFLT, true } -- Zalure Lv3
t[ 0x050C03 ] = { COLOR_DFLT, true } -- Zalure Lv4
t[ 0x050C04 ] = { COLOR_DFLT, true } -- Zalure Lv5
t[ 0x050C05 ] = { COLOR_DFLT, true } -- Zalure Lv6
t[ 0x050C06 ] = { COLOR_DFLT, true } -- Zalure Lv7
t[ 0x050C07 ] = { COLOR_DFLT, true } -- Zalure Lv8
t[ 0x050C08 ] = { COLOR_DFLT, true } -- Zalure Lv9
t[ 0x050C09 ] = { COLOR_DFLT, true } -- Zalure Lv10
t[ 0x050C0A ] = { COLOR_DFLT, true } -- Zalure Lv11
t[ 0x050C0B ] = { COLOR_DFLT, true } -- Zalure Lv12
t[ 0x050C0C ] = { COLOR_DFLT, true } -- Zalure Lv13
t[ 0x050C0D ] = { COLOR_DFLT, true } -- Zalure Lv14
t[ 0x050C0E ] = { COLOR_GOOD_TECHS, true } -- Zalure Lv15
t[ 0x050C0F ] = { COLOR_DFLT, true } -- Zalure Lv16
t[ 0x050C10 ] = { COLOR_DFLT, true } -- Zalure Lv17
t[ 0x050C11 ] = { COLOR_DFLT, true } -- Zalure Lv18
t[ 0x050C12 ] = { COLOR_DFLT, true } -- Zalure Lv19
t[ 0x050C13 ] = { COLOR_GOOD_TECHS, true } -- Zalure Lv20
t[ 0x050C14 ] = { COLOR_DFLT, true } -- Zalure Lv21
t[ 0x050C15 ] = { COLOR_DFLT, true } -- Zalure Lv22
t[ 0x050C16 ] = { COLOR_DFLT, true } -- Zalure Lv23
t[ 0x050C17 ] = { COLOR_DFLT, true } -- Zalure Lv24
t[ 0x050C18 ] = { COLOR_DFLT, true } -- Zalure Lv25
t[ 0x050C19 ] = { COLOR_DFLT, true } -- Zalure Lv26
t[ 0x050C1A ] = { COLOR_DFLT, true } -- Zalure Lv27
t[ 0x050C1B ] = { COLOR_DFLT, true } -- Zalure Lv28
t[ 0x050C1C ] = { COLOR_DFLT, true } -- Zalure Lv29
t[ 0x050C1D ] = { COLOR_GOOD_TECHS, true } -- Zalure Lv30
t[ 0x050D00 ] = { COLOR_DFLT, true } -- Shifta Lv1
t[ 0x050D01 ] = { COLOR_DFLT, true } -- Shifta Lv2
t[ 0x050D02 ] = { COLOR_DFLT, true } -- Shifta Lv3
t[ 0x050D03 ] = { COLOR_DFLT, true } -- Shifta Lv4
t[ 0x050D04 ] = { COLOR_DFLT, true } -- Shifta Lv5
t[ 0x050D05 ] = { COLOR_DFLT, true } -- Shifta Lv6
t[ 0x050D06 ] = { COLOR_DFLT, true } -- Shifta Lv7
t[ 0x050D07 ] = { COLOR_DFLT, true } -- Shifta Lv8
t[ 0x050D08 ] = { COLOR_DFLT, true } -- Shifta Lv9
t[ 0x050D09 ] = { COLOR_DFLT, true } -- Shifta Lv10
t[ 0x050D0A ] = { COLOR_DFLT, true } -- Shifta Lv11
t[ 0x050D0B ] = { COLOR_DFLT, true } -- Shifta Lv12
t[ 0x050D0C ] = { COLOR_DFLT, true } -- Shifta Lv13
t[ 0x050D0D ] = { COLOR_DFLT, true } -- Shifta Lv14
t[ 0x050D0E ] = { COLOR_GOOD_TECHS, true } -- Shifta Lv15
t[ 0x050D0F ] = { COLOR_DFLT, true } -- Shifta Lv16
t[ 0x050D10 ] = { COLOR_DFLT, true } -- Shifta Lv17
t[ 0x050D11 ] = { COLOR_DFLT, true } -- Shifta Lv18
t[ 0x050D12 ] = { COLOR_DFLT, true } -- Shifta Lv19
t[ 0x050D13 ] = { COLOR_GOOD_TECHS, true } -- Shifta Lv20
t[ 0x050D14 ] = { COLOR_DFLT, true } -- Shifta Lv21
t[ 0x050D15 ] = { COLOR_DFLT, true } -- Shifta Lv22
t[ 0x050D16 ] = { COLOR_DFLT, true } -- Shifta Lv23
t[ 0x050D17 ] = { COLOR_DFLT, true } -- Shifta Lv24
t[ 0x050D18 ] = { COLOR_DFLT, true } -- Shifta Lv25
t[ 0x050D19 ] = { COLOR_DFLT, true } -- Shifta Lv26
t[ 0x050D1A ] = { COLOR_DFLT, true } -- Shifta Lv27
t[ 0x050D1B ] = { COLOR_DFLT, true } -- Shifta Lv28
t[ 0x050D1C ] = { COLOR_DFLT, true } -- Shifta Lv29
t[ 0x050D1D ] = { COLOR_GOOD_TECHS, true } -- Shifta Lv30
t[ 0x050E00 ] = { COLOR_GOOD_TECHS, true } -- Ryuker Lv1
t[ 0x050F00 ] = { COLOR_DFLT, true } -- Resta Lv1
t[ 0x050F01 ] = { COLOR_DFLT, true } -- Resta Lv2
t[ 0x050F02 ] = { COLOR_DFLT, true } -- Resta Lv3
t[ 0x050F03 ] = { COLOR_DFLT, true } -- Resta Lv4
t[ 0x050F04 ] = { COLOR_DFLT, true } -- Resta Lv5
t[ 0x050F05 ] = { COLOR_DFLT, true } -- Resta Lv6
t[ 0x050F06 ] = { COLOR_DFLT, true } -- Resta Lv7
t[ 0x050F07 ] = { COLOR_DFLT, true } -- Resta Lv8
t[ 0x050F08 ] = { COLOR_DFLT, true } -- Resta Lv9
t[ 0x050F09 ] = { COLOR_DFLT, true } -- Resta Lv10
t[ 0x050F0A ] = { COLOR_DFLT, true } -- Resta Lv11
t[ 0x050F0B ] = { COLOR_DFLT, true } -- Resta Lv12
t[ 0x050F0C ] = { COLOR_DFLT, true } -- Resta Lv13
t[ 0x050F0D ] = { COLOR_DFLT, true } -- Resta Lv14
t[ 0x050F0E ] = { COLOR_GOOD_TECHS, true } -- Resta Lv15
t[ 0x050F0F ] = { COLOR_DFLT, true } -- Resta Lv16
t[ 0x050F10 ] = { COLOR_DFLT, true } -- Resta Lv17
t[ 0x050F11 ] = { COLOR_DFLT, true } -- Resta Lv18
t[ 0x050F12 ] = { COLOR_DFLT, true } -- Resta Lv19
t[ 0x050F13 ] = { COLOR_GOOD_TECHS, true } -- Resta Lv20
t[ 0x050F14 ] = { COLOR_DFLT, true } -- Resta Lv21
t[ 0x050F15 ] = { COLOR_DFLT, true } -- Resta Lv22
t[ 0x050F16 ] = { COLOR_DFLT, true } -- Resta Lv23
t[ 0x050F17 ] = { COLOR_DFLT, true } -- Resta Lv24
t[ 0x050F18 ] = { COLOR_DFLT, true } -- Resta Lv25
t[ 0x050F19 ] = { COLOR_DFLT, true } -- Resta Lv26
t[ 0x050F1A ] = { COLOR_DFLT, true } -- Resta Lv27
t[ 0x050F1B ] = { COLOR_DFLT, true } -- Resta Lv28
t[ 0x050F1C ] = { COLOR_DFLT, true } -- Resta Lv29
t[ 0x050F1D ] = { COLOR_GOOD_TECHS, true } -- Resta Lv30
t[ 0x051000 ] = { COLOR_DFLT, true } -- Anti Lv1
t[ 0x051001 ] = { COLOR_DFLT, true } -- Anti Lv2
t[ 0x051002 ] = { COLOR_DFLT, true } -- Anti Lv3
t[ 0x051003 ] = { COLOR_DFLT, true } -- Anti Lv4
t[ 0x051004 ] = { COLOR_GOOD_TECHS, true } -- Anti Lv5
t[ 0x051005 ] = { COLOR_DFLT, true } -- Anti Lv6
t[ 0x051006 ] = { COLOR_GOOD_TECHS, true } -- Anti Lv7
t[ 0x051100 ] = { COLOR_GOOD_TECHS, true } -- Reverser Lv1
t[ 0x051200 ] = { COLOR_DFLT, true } -- Megid Lv1
t[ 0x051201 ] = { COLOR_DFLT, true } -- Megid Lv2
t[ 0x051202 ] = { COLOR_DFLT, true } -- Megid Lv3
t[ 0x051203 ] = { COLOR_DFLT, true } -- Megid Lv4
t[ 0x051204 ] = { COLOR_DFLT, true } -- Megid Lv5
t[ 0x051205 ] = { COLOR_DFLT, true } -- Megid Lv6
t[ 0x051206 ] = { COLOR_DFLT, true } -- Megid Lv7
t[ 0x051207 ] = { COLOR_DFLT, true } -- Megid Lv8
t[ 0x051208 ] = { COLOR_DFLT, true } -- Megid Lv9
t[ 0x051209 ] = { COLOR_DFLT, true } -- Megid Lv10
t[ 0x05120A ] = { COLOR_DFLT, true } -- Megid Lv11
t[ 0x05120B ] = { COLOR_DFLT, true } -- Megid Lv12
t[ 0x05120C ] = { COLOR_DFLT, true } -- Megid Lv13
t[ 0x05120D ] = { COLOR_DFLT, true } -- Megid Lv14
t[ 0x05120E ] = { COLOR_DFLT, true } -- Megid Lv15
t[ 0x05120F ] = { COLOR_DFLT, true } -- Megid Lv16
t[ 0x051210 ] = { COLOR_DFLT, true } -- Megid Lv17
t[ 0x051211 ] = { COLOR_DFLT, true } -- Megid Lv18
t[ 0x051212 ] = { COLOR_DFLT, true } -- Megid Lv19
t[ 0x051213 ] = { COLOR_DFLT, true } -- Megid Lv20
t[ 0x051214 ] = { COLOR_DFLT, true } -- Megid Lv21
t[ 0x051215 ] = { COLOR_DFLT, true } -- Megid Lv22
t[ 0x051216 ] = { COLOR_DFLT, true } -- Megid Lv23
t[ 0x051217 ] = { COLOR_DFLT, true } -- Megid Lv24
t[ 0x051218 ] = { COLOR_DFLT, true } -- Megid Lv25
t[ 0x051219 ] = { COLOR_GOOD_TECHS, true } -- Megid Lv26
t[ 0x05121A ] = { COLOR_GOOD_TECHS, true } -- Megid Lv27
t[ 0x05121B ] = { COLOR_GOOD_TECHS, true } -- Megid Lv28
t[ 0x05121C ] = { COLOR_GOOD_TECHS, true } -- Megid Lv29
t[ 0x05121D ] = { COLOR_RARE, true } -- Megid Lv30

local function AddServerItems(server)
    if server == 1 then -- Vanilla

    elseif server == 2 then -- Ultima
        t[ 0x000109 ] = { COLOR_RARE, true } -- Tsumikiri J-Saber
        t[ 0x000208 ] = { COLOR_RARE, true } -- Rainbow Petrik
        t[ 0x000209 ] = { COLOR_RARE, true } -- Sil Dragon Slayer
        t[ 0x00030A ] = { COLOR_RARE, true } -- Blood Tornado
        t[ 0x000509 ] = { COLOR_RARE, true } -- Slicer of Vengeance
        t[ 0x00050A ] = { COLOR_RARE, true } -- Boomerang
        t[ 0x000609 ] = { COLOR_RARE, true } -- Morolian Blaster
        t[ 0x00070E ] = { COLOR_RARE, true } --	Lindcray
        t[ 0x00070F ] = { COLOR_RARE, true } --	Water Gun
        t[ 0x000908 ] = { COLOR_RARE, true } -- Charge Arms
        t[ 0x000909 ] = { COLOR_RARE, true } -- Crush Cannon
        t[ 0x001102 ] = { COLOR_RARE, true } -- Great Fairy Sword
        t[ 0x001201 ] = { COLOR_RARE, true } -- S.T.A.R.S. Spread
        t[ 0x001202 ] = { COLOR_RARE, true } -- Frosty Icicle Shooter
        t[ 0x001203 ] = { COLOR_RARE, true } -- Arrest Needle
        t[ 0x001301 ] = { COLOR_RARE, true } --	Last Holy Ray
        t[ 0x001403 ] = { COLOR_RARE, true } -- Captain Combat
        t[ 0x001404 ] = { COLOR_RARE, true } -- Sacred Bow
        t[ 0x001502 ] = { COLOR_RARE, true } -- Fire Rod
        t[ 0x001B01 ] = { COLOR_RARE, true } -- Ultima Bringer's
        t[ 0x001C01 ] = { COLOR_RARE, true } -- Egg Blaster mk2
        t[ 0x001D01 ] = { COLOR_RARE, true } -- Psycho Bridge
        t[ 0x002B01 ] = { COLOR_RARE, true } -- Magic Hammer
        t[ 0x003002 ] = { COLOR_RARE, true } -- Inferno Girasole
        t[ 0x003201 ] = { COLOR_RARE, true } -- Master Sword
        t[ 0x003401 ] = { COLOR_RARE, true } -- Crimson Sword
        t[ 0x004001 ] = { COLOR_RARE, true } -- Kiss of Death
        t[ 0x004302 ] = { COLOR_RARE, true } -- Serene Swan
        t[ 0x004801 ] = { COLOR_RARE, true } -- Samba's Fiesta
        t[ 0x004901 ] = { COLOR_RARE, true } -- Psycho Raven
        t[ 0x004E02 ] = { COLOR_RARE, true } -- Frozen Faust
        t[ 0x004E03 ] = { COLOR_RARE, true } -- Arrest Faust
        t[ 0x005C01 ] = { COLOR_RARE, true } -- Quasar Staff
        t[ 0x006201 ] = { COLOR_RARE, true } -- Macho Blades
        t[ 0x006A01 ] = { COLOR_RARE, true } -- Hand of Justice
        t[ 0x006E02 ] = { COLOR_RARE, true } --	Zelda Magazine
        t[ 0x006E03 ] = { COLOR_RARE, true } --	Sonic Magazine
        t[ 0x008904 ] = { COLOR_RARE, true } -- Tyrfing
        t[ 0x008B04 ] = { COLOR_RARE, true } -- Outlaw Star
        t[ 0x009701 ] = { COLOR_RARE, true } -- Zanbacon
        t[ 0x009E01 ] = { COLOR_RARE, true } -- Dark Meteor Storm
        t[ 0x00AC01 ] = { COLOR_RARE, true } -- Hundred Souls
        t[ 0x00AD04 ] = { COLOR_RARE, true } -- Rage de Glace
        t[ 0x00AE01 ] = { COLOR_RARE, true } --	Stealth Sword
        t[ 0x00B001 ] = { COLOR_RARE, true } -- Mille Faucilles
        t[ 0x00B301 ] = { COLOR_RARE, true } -- Varvienne
        t[ 0x00B501 ] = { COLOR_RARE, true } -- Power Glove
        t[ 0x00B801 ] = { COLOR_RARE, true } -- Ten Years Blades
        t[ 0x00B901 ] = { COLOR_RARE, true } -- Blood Sword
        t[ 0x00BF01 ] = { COLOR_RARE, true } -- Asteron Striker
        t[ 0x00C001 ] = { COLOR_RARE, true } -- Den's Hand Cannon
        t[ 0x00C002 ] = { COLOR_RARE, true } -- Banana Cannon
        t[ 0x00C003 ] = { COLOR_RARE, true } --	Bomb-Chu
        t[ 0x00C501 ] = { COLOR_RARE, true } -- Glide Divine V.00
        t[ 0x00CB01 ] = { COLOR_RARE, true } -- Rico's Parasol
        t[ 0x00D100 ] = { COLOR_RARE, true } -- Sword of Ultima
        t[ 0x00D701 ] = { COLOR_RARE, true } -- Bug-Catching Net
        t[ 0x00E302 ] = { COLOR_RARE, true } -- Fury of the Beast
        t[ 0x010158 ] = { COLOR_RARE, true } -- Sue's Coat
        t[ 0x010159 ] = { COLOR_RARE, true } -- Godric's Cloak
        t[ 0x01015A ] = { COLOR_RARE, true } --	Vampire Cloak
        t[ 0x01015B ] = { COLOR_RARE, true } -- Neutron Skin
        t[ 0x010273 ] = { COLOR_RARE, true } -- Anti-Dark Ring
        t[ 0x01027B ] = { COLOR_RARE, true } -- Anti-Light Ring
        t[ 0x01029A ] = { COLOR_RARE, true } -- Hylian Shield
        t[ 0x0102A5 ] = { COLOR_RARE, true } -- Virus Shield: Vol Opt
        t[ 0x0102A6 ] = { COLOR_RARE, true } --	Hylian Shield
        t[ 0x0102A7 ] = { COLOR_RARE, true } -- Frank The Bunny Costume
        t[ 0x0102A8 ] = { COLOR_RARE, true } -- Cat Scratch Fever Costume
        t[ 0x0102A9 ] = { COLOR_RARE, true } -- Guardian Angel Costume
        t[ 0x0102AA ] = { COLOR_RARE, true } -- Ylvis Costume
        t[ 0x0102AB ] = { COLOR_RARE, true } -- Agent K Costume
        t[ 0x0102AC ] = { COLOR_RARE, true } -- Bewbs Costume
        t[ 0x0102AD ] = { COLOR_RARE, true } -- Minecraft Costume
        t[ 0x0102AE ] = { COLOR_RARE, true } -- Alis' Resolve
        t[ 0x0102AF ] = { COLOR_RARE, true } --	Blue Dream
        t[ 0x0102B0 ] = { COLOR_RARE, true } --	White Widow
        t[ 0x0102B1 ] = { COLOR_RARE, true } -- Bruce Banner
        t[ 0x0102B2 ] = { COLOR_RARE, true } -- AK-47
        t[ 0x0102B3 ] = { COLOR_RARE, true } --	Girl Scout Cookies
        t[ 0x0102B4 ] = { COLOR_RARE, true } -- Ganondorf Shield
        t[ 0x010364 ] = { COLOR_RARE, true } --	Centurion/Battle
        t[ 0x010365 ] = { COLOR_RARE, true } -- Trap Search
        t[ 0x010366 ] = { COLOR_RARE, true } -- Centurion/Resist
        t[ 0x010367 ] = { COLOR_RARE, true } -- Godric's/Ability
        t[ 0x010368 ] = { COLOR_RARE, true } -- Centurion/Power
        t[ 0x010369 ] = { COLOR_RARE, true } -- Centurion/Luck
        t[ 0x01036A ] = { COLOR_RARE, true } -- Centurion/Arms
        t[ 0x01036B ] = { COLOR_RARE, true } -- Heart Cointainer
        t[ 0x01036C ] = { COLOR_RARE, true } --	Abuelita/Battle
        t[ 0x01036D ] = { COLOR_RARE, true } --	Centurion/Mind
        t[ 0x01036E ] = { COLOR_RARE, true } --	Centurion/Legs
        t[ 0x01036F ] = { COLOR_RARE, true } --	Centurion/Body
        t[ 0x010370 ] = { COLOR_RARE, true } --	Centurion/HP
        t[ 0x010371 ] = { COLOR_RARE, true } --	Centurion/TP
        t[ 0x010372 ] = { COLOR_RARE, true } --	Centurion/Technique
        t[ 0x024A00 ] = { COLOR_RARE, true } -- Ultima Mag!
        t[ 0x024D00 ] = { COLOR_RARE, true } -- Love Rappy
        t[ 0x030E28 ] = { COLOR_RARE, true } -- Psycho Black Crystal
        t[ 0x030E29 ] = { COLOR_RARE, true } -- Soul Booster
        t[ 0x030E2A ] = { COLOR_RARE, true } --	Frozen Booster
        t[ 0x030E2B ] = { COLOR_RARE, true } --	Piercing Photon
        t[ 0x030E2C ] = { COLOR_RARE, true } --	Halo Rappy Soul
        t[ 0x030E2D ] = { COLOR_RARE, true } --	Chromatic Orb
        t[ 0x030E2E ] = { COLOR_RARE, true } -- Harmonic Ressonance Core
        t[ 0x030E2F ] = { COLOR_RARE, true } -- Stellar Shard
        t[ 0x030E30 ] = { COLOR_RARE, true } -- Arrest Booster
        t[ 0x031214 ] = { COLOR_RARE, true } -- OvenMitts Booster
        t[ 0x031808 ] = { COLOR_RARE, true } -- Ultima!'s engine
        t[ 0x03180B ] = { COLOR_RARE, true } -- Love Rappy's Beak
        t[ 0x031810 ] = { COLOR_RARE, true } --	Ashura Mag Cell
    elseif server == 3 then -- Ephinea
        t[ 0x000109 ] = { COLOR_DFLT, true } -- Rapier
        t[ 0x00010A ] = { COLOR_DFLT, true } -- Cutlass
        t[ 0x00010B ] = { COLOR_DFLT, true } -- Falchion
        t[ 0x000208 ] = { COLOR_DFLT, true } -- Spatha
        t[ 0x000209 ] = { COLOR_DFLT, true } -- Pallade
        t[ 0x00020A ] = { COLOR_DFLT, true } -- Bastard
        t[ 0x00030A ] = { COLOR_DFLT, true } -- Anlace
        t[ 0x00030B ] = { COLOR_DFLT, true } -- Dirk
        t[ 0x00030C ] = { COLOR_DFLT, true } -- Stylet
        t[ 0x000409 ] = { COLOR_DFLT, true } -- Gisarme
        t[ 0x00040A ] = { COLOR_DFLT, true } -- Cleaver
        t[ 0x00040B ] = { COLOR_DFLT, true } -- Fauchard
        t[ 0x00040C ] = { COLOR_RARE, true } -- Asteron Belt* - Plantain Huge Fan
        t[ 0x00040D ] = { COLOR_RARE, true } -- Vjaya* - Plantain Huge Fan
        t[ 0x00040E ] = { COLOR_RARE, true } -- Vjaya* - Chameleon Scythe
        t[ 0x00040F ] = { COLOR_RARE, true } -- Vjaya* - Soul Banish
        t[ 0x000410 ] = { COLOR_RARE, true } -- Asteron Belt* - Chameleon Scythe
        t[ 0x000411 ] = { COLOR_RARE, true } -- Asteron Belt* - Soul Banish
        t[ 0x000509 ] = { COLOR_DFLT, true } -- Fan
        t[ 0x00050A ] = { COLOR_DFLT, true } -- Chain
        t[ 0x00050B ] = { COLOR_DFLT, true } -- Ricochet
        t[ 0x000609 ] = { COLOR_DFLT, true } -- Rotogun
        t[ 0x00060A ] = { COLOR_DFLT, true } -- Flintgun
        t[ 0x00060B ] = { COLOR_DFLT, true } -- Coilgun
        t[ 0x00070E ] = { COLOR_DFLT, true } -- Musket
        t[ 0x00070F ] = { COLOR_DFLT, true } -- Tracer
        t[ 0x000710 ] = { COLOR_DFLT, true } -- Deadshot
        t[ 0x00050C ] = { COLOR_RARE, true } -- Diska of Braveman* - Diska of Liberator
        t[ 0x00050D ] = { COLOR_RARE, true } -- Diska of Braveman* - Izmaela
        t[ 0x000808 ] = { COLOR_RARE, true } -- M&A60 Vise* - Yasminkov 9000M
        t[ 0x000809 ] = { COLOR_DFLT, true } -- Chopper
        t[ 0x00080A ] = { COLOR_DFLT, true } -- Drum
        t[ 0x00080B ] = { COLOR_DFLT, true } -- Volley
        t[ 0x00080C ] = { COLOR_RARE, true } -- M&A60 Vise* - Samba Maracas
        t[ 0x000908 ] = { COLOR_DFLT, true } -- Mortar
        t[ 0x000909 ] = { COLOR_DFLT, true } -- Ballista
        t[ 0x00090A ] = { COLOR_DFLT, true } -- Artillery
        t[ 0x000A08 ] = { COLOR_DFLT, true } -- Mallet
        t[ 0x000A09 ] = { COLOR_DFLT, true } -- Sledge
        t[ 0x000A0A ] = { COLOR_DFLT, true } -- Bludgeon
        t[ 0x000B08 ] = { COLOR_DFLT, true } -- Switch
        t[ 0x000B09 ] = { COLOR_DFLT, true } -- Spike
        t[ 0x000C08 ] = { COLOR_DFLT, true } -- Weaver
        t[ 0x000C09 ] = { COLOR_DFLT, true } -- Reservoir
        t[ 0x001201 ] = { COLOR_RARE, true } -- Spread Needle* - Rianov 303SNR
        t[ 0x001B01 ] = { COLOR_RARE, true } -- Bringer's Rifle* - Rianov 303SNR
        t[ 0x001B02 ] = { COLOR_RARE, true } -- Bringer's Rifle* - Egg Blaster
        t[ 0x001B03 ] = { COLOR_RARE, true } -- Bringer's Rifle* - Angel Harp
        t[ 0x001D01 ] = { COLOR_RARE, true } -- Psycho Wand* - Rabbit Wand
        t[ 0x001D02 ] = { COLOR_RARE, true } -- Psycho Wand* - Sorcerer's Cane
        t[ 0x001E01 ] = { COLOR_RARE, true } -- Heaven Punisher* - Tension Blaster
        t[ 0x001E02 ] = { COLOR_RARE, true } -- Heaven Punisher* - Suppressed Gun
        t[ 0x001E03 ] = { COLOR_RARE, true } -- Heaven Punisher* - Ruby Bullet
        t[ 0x001E04 ] = { COLOR_RARE, true } -- Heaven Punisher* (Divine Filter)
        t[ 0x001E05 ] = { COLOR_RARE, true } -- Heaven Punisher* - Tension Blaster (Divine Filter)
        t[ 0x001E06 ] = { COLOR_RARE, true } -- Heaven Punisher* - Suppressed Gun  (Divine Filter)
        t[ 0x001E07 ] = { COLOR_RARE, true } -- Heaven Punisher* - Ruby Bullet     (Divine Filter)
        t[ 0x001E08 ] = { COLOR_RARE, true } -- Heaven Punisher* (Lock-on Filter)
        t[ 0x001E09 ] = { COLOR_RARE, true } -- Heaven Punisher* - Tension Blaster (Lock-on Filter)
        t[ 0x001E0A ] = { COLOR_RARE, true } -- Heaven Punisher* - Suppressed Gun  (Lock-on Filter)
        t[ 0x001E0B ] = { COLOR_RARE, true } -- Heaven Punisher* - Ruby Bullet     (Lock-on Filter)
        t[ 0x002101 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain
        t[ 0x002102 ] = { COLOR_RARE, true } -- Chain Sawd* - Crazy Tune
        t[ 0x002103 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Blue and Green)
        t[ 0x002104 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Rose)
        t[ 0x002105 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Chartreuse)
        t[ 0x002106 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Mauve)
        t[ 0x002107 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Cyan)
        t[ 0x002108 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Orange)
        t[ 0x002109 ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (White)
        t[ 0x00210A ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Green)
        t[ 0x00210B ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Blue)
        t[ 0x00210C ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Purple)
        t[ 0x00210D ] = { COLOR_RARE, true } -- Chain Sawd* - Daisy Chain (Red)
        t[ 0x002401 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Yellow)
        t[ 0x002402 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Blue and Green)
        t[ 0x002403 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Rose)
        t[ 0x002404 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Chartreuse)
        t[ 0x002405 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Mauve)
        t[ 0x002406 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Cyan)
        t[ 0x002407 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Orange)
        t[ 0x002408 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (White)
        t[ 0x002409 ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Green)
        t[ 0x00240A ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Blue)
        t[ 0x00240B ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Purple)
        t[ 0x00240C ] = { COLOR_RARE, true } -- Magical Piece* - The Sigh of a God (Red)
        t[ 0x002D01 ] = { COLOR_RARE, true } -- Red Saber* - DB's Saber
        t[ 0x002D02 ] = { COLOR_RARE, true } -- Red Saber* - Ancient Saber
        t[ 0x002D03 ] = { COLOR_RARE, true } -- Red Saber* - Delsaber's Buster
        t[ 0x002D04 ] = { COLOR_RARE, true } -- Red Saber* - Flamberge
        t[ 0x003201 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain
        t[ 0x003202 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Crazy Tune
        t[ 0x003203 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Lollipop
        t[ 0x003204 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Laconium Axe
        t[ 0x003205 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Blue and Green)
        t[ 0x003206 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Rose)
        t[ 0x003207 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Chartreuse)
        t[ 0x003208 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Mauve)
        t[ 0x003209 ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Cyan)
        t[ 0x00320A ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Orange)
        t[ 0x00320B ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (White)
        t[ 0x00320C ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Green)
        t[ 0x00320D ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Blue)
        t[ 0x00320E ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Purple)
        t[ 0x00320F ] = { COLOR_RARE, true } -- Tsumikiri J-Sword* - Daisy Chain (Red)
        t[ 0x003401 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain
        t[ 0x003402 ] = { COLOR_RARE, true } -- Red Sword* - Crazy Tune
        t[ 0x003403 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Blue and Green)
        t[ 0x003404 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Rose)
        t[ 0x003405 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Chartreuse)
        t[ 0x003406 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Mauve)
        t[ 0x003407 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Cyan)
        t[ 0x003408 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Orange)
        t[ 0x003409 ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (White)
        t[ 0x00340A ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Green)
        t[ 0x00340B ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Blue)
        t[ 0x00340C ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Purple)
        t[ 0x00340D ] = { COLOR_RARE, true } -- Red Sword* - Daisy Chain (Red)
        t[ 0x004502 ] = { COLOR_RARE, true } -- Frozen Shooter* - Rianov 303SNR
        t[ 0x004503 ] = { COLOR_RARE, true } -- Frozen Shooter* - Egg Blaster
        t[ 0x004504 ] = { COLOR_RARE, true } -- Snow Queen* - Egg Blaster
        t[ 0x004505 ] = { COLOR_RARE, true } -- Frozen Shooter* - Angel Harp
        t[ 0x004506 ] = { COLOR_RARE, true } -- Snow Queen* - Angel Harp
        t[ 0x004507 ] = { COLOR_RARE, true } -- Snow Queen* - Rianov 303SNR
        t[ 0x004B02 ] = { COLOR_RARE, true } -- Guld Milla* - Yasminkov 9000M
        t[ 0x004B03 ] = { COLOR_RARE, true } -- Dual Bird* - Yasminkov 9000M
        t[ 0x004B04 ] = { COLOR_RARE, true } -- Guld Milla* - Samba Maracas
        t[ 0x004B05 ] = { COLOR_RARE, true } -- Dual Bird* - Samba Maracas
        t[ 0x004F01 ] = { COLOR_RARE, true } -- Summit Moon* - Lollipop
        t[ 0x005801 ] = { COLOR_RARE, true } -- Striker of Chao* - Rabbit Wand
        t[ 0x005802 ] = { COLOR_RARE, true } -- Striker of Chao* - Sorcerer's Cane
        t[ 0x005A01 ] = { COLOR_RARE, true } -- Prophets of Motav* - The Sigh of a God
        t[ 0x008904 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (White)
        t[ 0x008905 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Green)
        t[ 0x008906 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Blue)
        t[ 0x008907 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Purple)
        t[ 0x008908 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Red)
        t[ 0x008909 ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Yellow)
        t[ 0x00890A ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Blue and Green)
        t[ 0x00890B ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Rose)
        t[ 0x00890C ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Chartreuse)
        t[ 0x00890D ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Mauve)
        t[ 0x00890E ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Cyan)
        t[ 0x00890F ] = { COLOR_RARE, true } -- Musashi* - TypeSS/Swords (Orange)
        t[ 0x008910 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (White)
        t[ 0x008911 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Green)
        t[ 0x008912 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Blue)
        t[ 0x008913 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Purple)
        t[ 0x008914 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Red)
        t[ 0x008915 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Yellow)
        t[ 0x008916 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Blue and Green)
        t[ 0x008917 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Rose)
        t[ 0x008918 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Chartreuse)
        t[ 0x008919 ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Mauve)
        t[ 0x00891A ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Cyan)
        t[ 0x00891B ] = { COLOR_RARE, true } -- Asuka* - TypeSS/Swords (Orange)
        t[ 0x00891C ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (White)
        t[ 0x00891D ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Green)
        t[ 0x00891E ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Blue)
        t[ 0x00891F ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Purple)
        t[ 0x008920 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Red)
        t[ 0x008921 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Yellow)
        t[ 0x008922 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Blue and Green)
        t[ 0x008923 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Rose)
        t[ 0x008924 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Chartreuse)
        t[ 0x008925 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Mauve)
        t[ 0x008926 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Cyan)
        t[ 0x008927 ] = { COLOR_RARE, true } -- Sange & Yasha* - TypeSS/Swords (Orange)
        t[ 0x008F09 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain
        t[ 0x008F0A ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Crazy Tune
        t[ 0x008F0B ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Blue and Green)
        t[ 0x008F0C ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Rose)
        t[ 0x008F0D ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Chartreuse)
        t[ 0x008F0E ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Mauve)
        t[ 0x008F0F ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Cyan)
        t[ 0x008F10 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Orange)
        t[ 0x008F11 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (White)
        t[ 0x008F12 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Green)
        t[ 0x008F13 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Blue)
        t[ 0x008F14 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Purple)
        t[ 0x008F15 ] = { COLOR_RARE, true } -- Flowen's Sword (3084)* - Daisy Chain (Red)
        t[ 0x009701 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain
        t[ 0x009702 ] = { COLOR_RARE, true } -- Zanba* - Crazy Tune
        t[ 0x009703 ] = { COLOR_RARE, true } -- Zanba* - Laconium Axe
        t[ 0x009704 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Blue and Green)
        t[ 0x009705 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Rose)
        t[ 0x009706 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Chartreuse)
        t[ 0x009707 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Mauve)
        t[ 0x009708 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Cyan)
        t[ 0x009709 ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Orange)
        t[ 0x00970A ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (White)
        t[ 0x00970B ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Green)
        t[ 0x00970C ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Blue)
        t[ 0x00970D ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Purple)
        t[ 0x00970E ] = { COLOR_RARE, true } -- Zanba* - Daisy Chain (Red)
        t[ 0x009C01 ] = { COLOR_RARE, true } -- Rainbow Baton* - Diska of Liberator
        t[ 0x009C02 ] = { COLOR_RARE, true } -- Rainbow Baton* - Izmaela
        t[ 0x009D01 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Yellow)
        t[ 0x009D02 ] = { COLOR_RARE, true } -- Dark Flow* - Crazy Tune
        t[ 0x009D03 ] = { COLOR_RARE, true } -- Dark Flow* - Ancient Saber
        t[ 0x009D04 ] = { COLOR_RARE, true } -- Dark Flow* - Laconium Axe
        t[ 0x009D05 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Blue and Green)
        t[ 0x009D06 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Rose)
        t[ 0x009D07 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Chartreuse)
        t[ 0x009D08 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Mauve)
        t[ 0x009D09 ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Cyan)
        t[ 0x009D0A ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Orange)
        t[ 0x009D0B ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (White)
        t[ 0x009D0C ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Green)
        t[ 0x009D0D ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Blue)
        t[ 0x009D0E ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Purple)
        t[ 0x009D0F ] = { COLOR_RARE, true } -- Dark Flow* - Daisy Chain (Red)
        t[ 0x00AA01 ] = { COLOR_RARE, true } -- Slicer of Fanatic* - Diska of Liberator
        t[ 0x00AA02 ] = { COLOR_RARE, true } -- Slicer of Fanatic* - Izmaela
        t[ 0x00AC01 ] = { COLOR_RARE, true } -- Excalibur* - Lollipop
        t[ 0x00AC02 ] = { COLOR_RARE, true } -- Excalibur* - DB's Saber
        t[ 0x00AC03 ] = { COLOR_RARE, true } -- Excalibur* - Ancient Saber
        t[ 0x00AC04 ] = { COLOR_RARE, true } -- Excalibur* - Delsaber's Buster
        t[ 0x00AC05 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Blue)
        t[ 0x00AC06 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Purple)
        t[ 0x00AC07 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Red)
        t[ 0x00AC08 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Yellow)
        t[ 0x00AC09 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Blue and Green)
        t[ 0x00AC0A ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Rose)
        t[ 0x00AC0B ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Chartreuse)
        t[ 0x00AC0C ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Mauve)
        t[ 0x00AC0D ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Cyan)
        t[ 0x00AC0E ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Orange)
        t[ 0x00AC0F ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (White)
        t[ 0x00AC10 ] = { COLOR_RARE, true } -- Excalibur* - Flamberge (Green)
        t[ 0x00B001 ] = { COLOR_RARE, true } -- Mille Marteaux* - Yasminkov 9000M
        t[ 0x00B002 ] = { COLOR_RARE, true } -- Mille Marteaux* - Samba Maracas
        t[ 0x00B003 ] = { COLOR_RARE, true } -- Mille Marteaux* (Divine Filter)
        t[ 0x00B004 ] = { COLOR_RARE, true } -- Mille Marteaux* - Yasminkov 9000M (Divine Filter)
        t[ 0x00B005 ] = { COLOR_RARE, true } -- Mille Marteaux* - Samba Maracas (Divine Filter)
        t[ 0x00B006 ] = { COLOR_RARE, true } -- Mille Marteaux* (Lock-on Filter)
        t[ 0x00B007 ] = { COLOR_RARE, true } -- Mille Marteaux* - Yasminkov 9000M (Lock-on Filter)
        t[ 0x00B008 ] = { COLOR_RARE, true } -- Mille Marteaux* - Samba Maracas (Lock-on Filter)
        t[ 0x00B301 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Purple)
        t[ 0x00B302 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Red)
        t[ 0x00B303 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Yellow)
        t[ 0x00B304 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Blue and Green)
        t[ 0x00B305 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Rose)
        t[ 0x00B306 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Chartreuse)
        t[ 0x00B307 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Mauve)
        t[ 0x00B308 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Cyan)
        t[ 0x00B309 ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Orange)
        t[ 0x00B30A ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (White)
        t[ 0x00B30B ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Green)
        t[ 0x00B30C ] = { COLOR_RARE, true } -- Vivienne* - TypeDS/D.Saber (Blue)
        t[ 0x00B30D ] = { COLOR_RARE, true } -- Vivienne* - Partisan of Lightning
        t[ 0x00B801 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (White)
        t[ 0x00B802 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Green)
        t[ 0x00B803 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Blue)
        t[ 0x00B804 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Purple)
        t[ 0x00B805 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Red)
        t[ 0x00B806 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Yellow)
        t[ 0x00B807 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Blue and Green)
        t[ 0x00B808 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Rose)
        t[ 0x00B809 ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Chartreuse)
        t[ 0x00B80A ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Mauve)
        t[ 0x00B80B ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Cyan)
        t[ 0x00B80C ] = { COLOR_RARE, true } -- Jizai* - TypeSS/Swords (Orange)
        t[ 0x00BA01 ] = { COLOR_RARE, true } -- Yunchang* - Plantain Huge Fan
        t[ 0x00BA02 ] = { COLOR_RARE, true } -- Yunchang* - Chameleon Scythe
        t[ 0x00BA03 ] = { COLOR_RARE, true } -- Yunchang* - Soul Banish
        t[ 0x00BF01 ] = { COLOR_RARE, true } -- Heaven Striker* - Tension Blaster
        t[ 0x00BF02 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Green)
        t[ 0x00BF03 ] = { COLOR_RARE, true } -- Heaven Striker* - Ruby Bullet
        t[ 0x00BF04 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Blue)
        t[ 0x00BF05 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Purple)
        t[ 0x00BF06 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Red)
        t[ 0x00BF07 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Yellow)
        t[ 0x00BF08 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Blue and Green)
        t[ 0x00BF09 ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Rose)
        t[ 0x00BF0A ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Chartreuse)
        t[ 0x00BF0B ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Mauve)
        t[ 0x00BF0C ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Cyan)
        t[ 0x00BF0D ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (Orange)
        t[ 0x00BF0E ] = { COLOR_RARE, true } -- Heaven Striker* - Suppressed Gun (White)
        t[ 0x00C501 ] = { COLOR_RARE, true } -- Glide Divine* - Rabbit Wand
        t[ 0x00C502 ] = { COLOR_RARE, true } -- Glide Divine* - Sorcerer's Cane
        t[ 0x00C801 ] = { COLOR_RARE, true } -- Daylight Scar* - Blade Dance
        t[ 0x00C802 ] = { COLOR_RARE, true } -- Daylight Scar* - Wok of Akiko's Shop
        t[ 0x00C803 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Green)
        t[ 0x00C804 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Blue)
        t[ 0x00C805 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Purple)
        t[ 0x00C806 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Red)
        t[ 0x00C807 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Yellow)
        t[ 0x00C808 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Blue and Green)
        t[ 0x00C809 ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Rose)
        t[ 0x00C80A ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Chartreuse)
        t[ 0x00C80B ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Mauve)
        t[ 0x00C80C ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Cyan)
        t[ 0x00C80D ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (Orange)
        t[ 0x00C80E ] = { COLOR_RARE, true } -- Daylight Scar* - Twin Chakram (White)
        t[ 0x010253 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Blue)
        t[ 0x01025B ] = { COLOR_RARE, true } -- Red Ring* (Skin: Green)
        t[ 0x010263 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Yellow)
        t[ 0x01026B ] = { COLOR_RARE, true } -- Red Ring* (Skin: Purple)
        t[ 0x010273 ] = { COLOR_RARE, true } -- Anti-Dark Ring
        t[ 0x010274 ] = { COLOR_RARE, true } -- Red Ring* (Skin: White)
        t[ 0x01027B ] = { COLOR_RARE, true } -- Anti-Light Ring
        t[ 0x01027C ] = { COLOR_RARE, true } -- Red Ring* (Skin: Black)
        t[ 0x0102A5 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Chartreuse)
        t[ 0x0102A6 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Cyan)
        t[ 0x0102A7 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Onyx)
        t[ 0x0102A8 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Orange)
        t[ 0x0102A9 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Rose)
        t[ 0x0102AA ] = { COLOR_RARE, true } -- Red Ring* (Skin: Ultramarine)
        t[ 0x0102AB ] = { COLOR_RARE, true } -- Red Ring* (Skin: Violet)
        t[ 0x0102AC ] = { COLOR_RARE, true } -- Red Ring* (Skin: Shield of Delsaber)
        t[ 0x0102AD ] = { COLOR_RARE, true } -- Red Ring* (Skin: Standstill Shield)
        t[ 0x0102AE ] = { COLOR_RARE, true } -- Red Ring* (Skin: Honeycomb Reflector)
        t[ 0x0102AF ] = { COLOR_RARE, true } -- Red Ring* (Skin: Epsiguard)
        t[ 0x0102B0 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Safety Heart)
        t[ 0x0102B1 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Angel Ring)
        t[ 0x0102B2 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Bunny Ears)
        t[ 0x0102B3 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Cat Ears)
        t[ 0x0102B4 ] = { COLOR_RARE, true } -- Red Ring* (Skin: From the Depths)
        t[ 0x0102B5 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Black Gear)
        t[ 0x0102B6 ] = { COLOR_RARE, true } -- Red Ring* (Skin: Ragol Ring)
        t[ 0x024D00 ] = { COLOR_RARE, true } -- Stealth
        t[ 0x024E00 ] = { COLOR_RARE, true } -- Mag*
        t[ 0x024F00 ] = { COLOR_RARE, true } -- Varuna*
        t[ 0x025000 ] = { COLOR_RARE, true } -- Kalki*
        t[ 0x025100 ] = { COLOR_RARE, true } -- Vritra*
        t[ 0x030E28 ] = { COLOR_RARE, true } -- Heart of Daisy Chain
        t[ 0x030E29 ] = { COLOR_RARE, true } -- Heart of Crazy Tune
        t[ 0x030E2A ] = { COLOR_RARE, true } -- Heart of Rianov 303SNR
        t[ 0x030E2B ] = { COLOR_RARE, true } -- Heart of Yasminkov 9000M
        t[ 0x030E2C ] = { COLOR_RARE, true } -- Heart of Rabbit Wand
        t[ 0x030E2D ] = { COLOR_RARE, true } -- Heart of Sorcerer's Cane
        t[ 0x030E2E ] = { COLOR_RARE, true } -- Neutralizer
        t[ 0x030E2F ] = { COLOR_RARE, true } -- Blue Paint
        t[ 0x030E30 ] = { COLOR_RARE, true } -- Green Paint
        t[ 0x030E31 ] = { COLOR_RARE, true } -- Yellow Paint
        t[ 0x030E32 ] = { COLOR_RARE, true } -- Purple Paint
        t[ 0x030E33 ] = { COLOR_RARE, true } -- White Paint
        t[ 0x030E34 ] = { COLOR_RARE, true } -- Black Paint
        t[ 0x030E35 ] = { COLOR_RARE, true } -- Red Paint
        t[ 0x030E36 ] = { COLOR_RARE, true } -- Heart of Suppressed Gun
        t[ 0x030E37 ] = { COLOR_RARE, true } -- Heart of Tension Blaster
        t[ 0x030E38 ] = { COLOR_RARE, true } -- Heart of Samba Maracas
        t[ 0x030E39 ] = { COLOR_RARE, true } -- Heart of Lollipop
        t[ 0x030E3A ] = { COLOR_RARE, true } -- Heart of DB's Saber
        t[ 0x030E3B ] = { COLOR_RARE, true } -- Heart of Plantain Huge Fan
        t[ 0x030E3C ] = { COLOR_RARE, true } -- Heart of Diska of Liberator
        t[ 0x030E3D ] = { COLOR_RARE, true } -- Heart of Izmaela
        t[ 0x030E3E ] = { COLOR_RARE, true } -- Heart of The Sigh of a God
        t[ 0x030E3F ] = { COLOR_RARE, true } -- Heart of Angel Harp
        t[ 0x030E40 ] = { COLOR_RARE, true } -- Heart of Ruby Bullet
        t[ 0x030E41 ] = { COLOR_RARE, true } -- Heart of Ancient Saber
        t[ 0x030E42 ] = { COLOR_RARE, true } -- Heart of Laconium Axe
        t[ 0x030E43 ] = { COLOR_RARE, true } -- Heart of Delsaber's Buster
        t[ 0x030E44 ] = { COLOR_RARE, true } -- Heart of Egg Blaster
        t[ 0x030E45 ] = { COLOR_RARE, true } -- Photon Filter
        t[ 0x030E46 ] = { COLOR_RARE, true } -- Heart of Flamberge
        t[ 0x030E47 ] = { COLOR_RARE, true } -- Heart of Chameleon Scythe
        t[ 0x030E48 ] = { COLOR_RARE, true } -- Heart of Soul Banish
        t[ 0x030E49 ] = { COLOR_RARE, true } -- Chartreuse Paint
        t[ 0x030E4A ] = { COLOR_RARE, true } -- Cyan Paint
        t[ 0x030E4B ] = { COLOR_RARE, true } -- Onyx Paint
        t[ 0x030E4C ] = { COLOR_RARE, true } -- Orange Paint
        t[ 0x030E4D ] = { COLOR_RARE, true } -- Rose Paint
        t[ 0x030E4E ] = { COLOR_RARE, true } -- Ultramarine Paint
        t[ 0x030E4F ] = { COLOR_RARE, true } -- Violet Paint
        t[ 0x030E50 ] = { COLOR_RARE, true } -- Delsaber Plating
        t[ 0x030E51 ] = { COLOR_RARE, true } -- Standstill Plating
        t[ 0x030E52 ] = { COLOR_RARE, true } -- Honeycomb Plating
        t[ 0x030E53 ] = { COLOR_RARE, true } -- Epsilon Plating
        t[ 0x030E54 ] = { COLOR_RARE, true } -- Heart Plating
        t[ 0x030E55 ] = { COLOR_RARE, true } -- Angel Plating
        t[ 0x030E56 ] = { COLOR_RARE, true } -- Bunny Plating
        t[ 0x030E57 ] = { COLOR_RARE, true } -- Cat Plating
        t[ 0x030E58 ] = { COLOR_RARE, true } -- Deep Plating
        t[ 0x030E59 ] = { COLOR_RARE, true } -- Gear Plating
        t[ 0x030E5A ] = { COLOR_RARE, true } -- Ragol Plating
        t[ 0x030E5B ] = { COLOR_RARE, true } -- Heart of TypeDS/D.Saber
        t[ 0x030E5C ] = { COLOR_RARE, true } -- Heart of TypeSS/Swords
        t[ 0x030E5D ] = { COLOR_RARE, true } -- Heart of Blade Dance
        t[ 0x030E5E ] = { COLOR_RARE, true } -- Heart of Wok of Akiko's Shop
        t[ 0x030E5F ] = { COLOR_RARE, true } -- Heart of Twin Chakram
        t[ 0x030E60 ] = { COLOR_RARE, true } -- Divine Filter
        t[ 0x030E61 ] = { COLOR_RARE, true } -- Lock-on Filter
        t[ 0x030E62 ] = { COLOR_RARE, true } -- Heart of Partisan of Lightning
        t[ 0x031005 ] = { COLOR_RARE, true } -- Event Egg
        t[ 0x031006 ] = { COLOR_RARE, true } -- 1st Anniv. Bronze Badge
        t[ 0x031007 ] = { COLOR_RARE, true } -- 1st Anniv. Silver Badge
        t[ 0x031008 ] = { COLOR_RARE, true } -- 1st Anniv. Gold Badge
        t[ 0x031009 ] = { COLOR_RARE, true } -- 1st Anniv. Platinum Badge
        t[ 0x03100A ] = { COLOR_RARE, true } -- 2nd Anniv. Bronze Badge
        t[ 0x03100B ] = { COLOR_RARE, true } -- 2nd Anniv. Silver Badge
        t[ 0x03100C ] = { COLOR_RARE, true } -- 2nd Anniv. Gold Badge
        t[ 0x03100D ] = { COLOR_RARE, true } -- 2nd Anniv. Platinum Badge
        t[ 0x031010 ] = { COLOR_RARE, true } -- 3rd Anniv. Bronze Badge
        t[ 0x031011 ] = { COLOR_RARE, true } -- 3rd Anniv. Silver Badge
        t[ 0x031012 ] = { COLOR_RARE, true } -- 3rd Anniv. Gold Badge
        t[ 0x031013 ] = { COLOR_RARE, true } -- 3rd Anniv. Platinum Badge
        t[ 0x031014 ] = { COLOR_RARE, true } -- Photon Hoard
        t[ 0x031015 ] = { COLOR_RARE, true } -- 4th Anniv. Bronze Badge
        t[ 0x031016 ] = { COLOR_RARE, true } -- 4th Anniv. Silver Badge
        t[ 0x031017 ] = { COLOR_RARE, true } -- 4th Anniv. Gold Badge
        t[ 0x031018 ] = { COLOR_RARE, true } -- 4th Anniv. Platinum Badge
        t[ 0x031019 ] = { COLOR_RARE, true } -- Anniv. Bronze Badge
        t[ 0x03101A ] = { COLOR_RARE, true } -- Anniv. Silver Badge
        t[ 0x03101B ] = { COLOR_RARE, true } -- Anniv. Gold Badge
        t[ 0x03101C ] = { COLOR_RARE, true } -- Anniv. Platinum Badge
        t[ 0x03100E ] = { COLOR_RARE, true } -- Halloween Cookie
        t[ 0x03100F ] = { COLOR_RARE, true } -- Coal
        t[ 0x03160C ] = { COLOR_MUSIC, true } -- Disk Vol.13 "Get It Up"
        t[ 0x03160D ] = { COLOR_MUSIC, true } -- Disk Vol.14 "Flight"
        t[ 0x03160E ] = { COLOR_MUSIC, true } -- Disk Vol.15 "Space Harrier"
        t[ 0x03160F ] = { COLOR_MUSIC, true } -- Disk Vol.16 "Deathwatch"
        t[ 0x031610 ] = { COLOR_MUSIC, true } -- Disk Vol.17 "Fly Me To The Moon"
        t[ 0x031611 ] = { COLOR_MUSIC, true } -- Disk Vol.18 "Puyo Puyo"
        t[ 0x031612 ] = { COLOR_MUSIC, true } -- Disk Vol.19 "Rhythm And Balance"
        t[ 0x031613 ] = { COLOR_MUSIC, true } -- Disk Vol.20 "The Party Must Go On"
        t[ 0x031614 ] = { COLOR_MUSIC, true } -- Disk Vol.21 "Armada Battle"
        t[ 0x031615 ] = { COLOR_MUSIC, true } -- Disk Vol.22 "Back 2 Back"
        t[ 0x031616 ] = { COLOR_MUSIC, true } -- Disk Vol.23 "The Strange Fruits"
        t[ 0x031617 ] = { COLOR_MUSIC, true } -- Disk Vol.24 "The Whims of Fate"
        t[ 0x031618 ] = { COLOR_MUSIC, true } -- Disk Vol.25 "Last Impression"
        t[ 0x031705 ] = { COLOR_RARE, true } -- Viridia Badge
        t[ 0x031706 ] = { COLOR_RARE, true } -- Greenill Badge
        t[ 0x031707 ] = { COLOR_RARE, true } -- Skyly Badge
        t[ 0x031708 ] = { COLOR_RARE, true } -- Bluefull Badge
        t[ 0x031709 ] = { COLOR_RARE, true } -- Purplenum Badge
        t[ 0x03170A ] = { COLOR_RARE, true } -- Pinkal Badge
        t[ 0x03170B ] = { COLOR_RARE, true } -- Redria Badge
        t[ 0x03170C ] = { COLOR_RARE, true } -- Oran Badge
        t[ 0x03170D ] = { COLOR_RARE, true } -- Yellowboze Badge
        t[ 0x03170E ] = { COLOR_RARE, true } -- Whitill Badge
        t[ 0x03180B ] = { COLOR_RARE, true } -- Stealth Kit
        t[ 0x03180C ] = { COLOR_RARE, true } -- Mag Kit
        t[ 0x03180D ] = { COLOR_RARE, true } -- Varuna Kit
        t[ 0x03180E ] = { COLOR_RARE, true } -- Kalki Kit
        t[ 0x03180F ] = { COLOR_RARE, true } -- Vritra Kit
        t[ 0x031810 ] = { COLOR_RARE, true } -- Heart of YN-0117
    elseif server == 4 then -- Schthack

    end
end

return
{
    AddServerItems = AddServerItems,
    t = t,
}
