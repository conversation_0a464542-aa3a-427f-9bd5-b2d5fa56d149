local core_mainmenu = require("core_mainmenu")
local lib_helpers = require("solylib.helpers")
local lib_menu = require("solylib.menu")
local lib_characters = require("solylib.characters")
local lib_unitxt = require("solylib.unitxt")
local requirements = require("Disk Tracker.data.requirements")
local cfg = require("Disk Tracker.configuration")
local optionsLoaded, options = pcall(require, "Disk Tracker.options")
local optionsFileName = "addons/Disk Tracker/options.lua"
local ConfigurationWindow
local Frame
local Disks = {}

if optionsLoaded then
    -- If options loaded, make sure we have all those we need
    options.configurationEnableWindow = lib_helpers.NotNilOrDefault(options.configurationEnableWindow, true)
    options.EnableWindow = lib_helpers.NotNilOrDefault(options.EnableWindow, true)
    options.HideWhenMenu = lib_helpers.NotNilOrDefault(options.HideWhenMenu, false)
    options.HideWhenSymbolChat = lib_helpers.NotNilOrDefault(options.HideWhenSymbolChat, false)
    options.HideWhenMenuUnavailable = lib_helpers.NotNilOrDefault(options.HideWhenMenuUnavailable, true)
    options.changed = lib_helpers.NotNilOrDefault(options.changed, true)
    options.Anchor = lib_helpers.NotNilOrDefault(options.Anchor, 1)
    options.X = lib_helpers.NotNilOrDefault(options.X, 50)
    options.Y = lib_helpers.NotNilOrDefault(options.Y, 50)
    options.W = lib_helpers.NotNilOrDefault(options.W, 500)
    options.H = lib_helpers.NotNilOrDefault(options.H, 500)
    options.NoTitleBar = lib_helpers.NotNilOrDefault(options.NoTitleBar, "")
    options.NoResize = lib_helpers.NotNilOrDefault(options.NoResize, "")
    options.NoMove = lib_helpers.NotNilOrDefault(options.NoMove, "")
    options.AlwaysAutoResize = lib_helpers.NotNilOrDefault(options.AlwaysAutoResize, "")
    options.TransparentWindow = lib_helpers.NotNilOrDefault(options.TransparentWindow, false)
else
    options =
    {
        configurationEnableWindow = true,
        EnableWindow = true,
        HideWhenMenu = false,
        HideWhenSymbolChat = false,
        HideWhenMenuUnavailable = true,
        changed = true,
        Anchor = 1,
        X = 50,
        Y = 50,
        W = 500,
        H = 500,
        NoTitleBar = "",
        NoResize = "",
        NoMove = "",
        AlwaysAutoResize = "",
        TransparentWindow = false,
    }
end

local function SaveOptions(options)
    local file = io.open(optionsFileName, "w")
    if file ~= nil then
        io.output(file)

        io.write("return\n")
        io.write("{\n")
        io.write(string.format("    configurationEnableWindow = %s,\n", tostring(options.configurationEnableWindow)))
        io.write(string.format("    EnableWindow = %s,\n", tostring(options.EnableWindow)))
        io.write(string.format("    HideWhenMenu = %s,\n", tostring(options.HideWhenMenu)))
        io.write(string.format("    HideWhenSymbolChat = %s,\n", tostring(options.HideWhenSymbolChat)))
        io.write(string.format("    HideWhenMenuUnavailable = %s,\n", tostring(options.HideWhenMenuUnavailable)))
        io.write(string.format("    Anchor = %i,\n", options.Anchor))
        io.write(string.format("    X = %i,\n", options.X))
        io.write(string.format("    Y = %i,\n", options.Y))
        io.write(string.format("    W = %i,\n", options.W))
        io.write(string.format("    H = %i,\n", options.H))
        io.write(string.format("    NoTitleBar = \"%s\",\n", options.NoTitleBar))
        io.write(string.format("    NoResize = \"%s\",\n", options.NoResize))
        io.write(string.format("    NoMove = \"%s\",\n", options.NoMove))
        io.write(string.format("    AlwaysAutoResize = \"%s\",\n", options.AlwaysAutoResize))
        io.write(string.format("    TransparentWindow = %s,\n", tostring(options.TransparentWindow)))
        io.write("}\n")

        io.close(file)
    end
end

local function SaveCharacterData(player, mind, techniques)
    local charFileName = "addons/Disk Tracker/data/" .. player .. ".lua"
    local file = io.open(charFileName, "w")
    if file ~= nil then
        io.output(file)
        io.write("return\n")
        io.write("{\n")
        io.write(string.format("    mind = %i,\n", mind))
        io.write("    techniques = {\n")
        for tech, level in pairs(techniques) do
            io.write(string.format("        %s = %i,\n", tech, level))
        end
        io.write("    },\n")
        io.write("}\n")
        io.close(file)
    end
end

local function ParseTechniqueDisk(itemString)
    local techName, techLevel = string.match(itemString, "(.+) Lv(%d+)")
    if techName and techLevel then
        return techName, tonumber(techLevel)
    end
    return nil
end

local function AddOrUpdateDisk(disks, techName, techLevel, loc)
    local locPriority = { B = 3, S = 2, I = 1 }

    for i, disk in ipairs(disks) do
        if disk.level == techLevel then
            if locPriority[loc] > locPriority[disk.loc] then
                disks[i] = { level = techLevel, loc = loc }
            end
            return
        end
    end
    table.insert(disks, { level = techLevel, loc = loc })
end

local function ReadDiskData()
    local charsLoaded, chars = pcall(require, "Backpack.data.chars")
    if not charsLoaded or chars == nil then
        return
    end

    local newDisks = {}

    for charName, _ in pairs(chars) do
        newDisks[charName] = {}
    end

    -- Read shared bank
    local sharedBankLoaded, sharedBank = pcall(require, "Backpack.data.shared_bank")
    if sharedBankLoaded and sharedBank ~= nil then
        for _, itemStr in pairs(sharedBank) do
            local techName, techLevel = ParseTechniqueDisk(itemStr)
            if techName then
                for charName, _ in pairs(chars) do
                    if newDisks[charName][techName] == nil then newDisks[charName][techName] = {} end
                    AddOrUpdateDisk(newDisks[charName][techName], techName, techLevel, "S")
                end
            end
        end
    end

    for charName, _ in pairs(chars) do
        -- Read bank
        local bankFile = "Backpack.data." .. charName .. "_bank"
        local bankLoaded, bank = pcall(require, bankFile)
        if bankLoaded and bank ~= nil then
            for _, itemStr in pairs(bank) do
                local techName, techLevel = ParseTechniqueDisk(itemStr)
                if techName then
                    if newDisks[charName][techName] == nil then newDisks[charName][techName] = {} end
                    AddOrUpdateDisk(newDisks[charName][techName], techName, techLevel, "B")
                end
            end
        end

        -- Read inventory
        local invFile = "Backpack.data." .. charName .. "_inv"
        local invLoaded, inv = pcall(require, invFile)
        if invLoaded and inv ~= nil then
            for _, itemStr in pairs(inv) do
                local techName, techLevel = ParseTechniqueDisk(itemStr)
                if techName then
                    if newDisks[charName][techName] == nil then newDisks[charName][techName] = {} end
                    AddOrUpdateDisk(newDisks[charName][techName], techName, techLevel, "I")
                end
            end
        end
    end
    Disks = newDisks
end

local function TrackCharacterData()
    if Frame % 60 == 0 then
        ReadDiskData()
    end

    local playerAddress = lib_characters.GetSelf()
    if playerAddress == 0 then return end

    local name = lib_characters.GetPlayerName(playerAddress)
    if name == nil or name == "" then return end

    local classId = lib_characters.GetPlayerClass(playerAddress)
    local className = lib_unitxt.GetClassName(classId)
    local sectionId = lib_characters.GetPlayerSectionID(playerAddress)
    local sectionIdName = lib_unitxt.GetSectionIDName(sectionId)

    local isAndroid = className == "HUcast" or className == "RAcast" or className == "RAcaseal" or className == "HUcaseal"
    if isAndroid then return end

    if Frame >= 30 then
        local mind = lib_characters.GetPlayerMST(playerAddress)
        local techniques = {}
        for techName, techId in pairs(lib_characters.Techniques) do
            local level = lib_characters.GetPlayerTechniqueLevel(playerAddress, techId)
            if level > 0 then
                techniques[techName] = level
            end
        end

        local char = tostring(name .. '~~~' .. className .. '~~~' .. sectionIdName)
        SaveCharacterData(char, mind, techniques)
        Frame = 0
    end
    Frame = Frame + 1
end

local function PresentDiskTracker()
    local characters = {}
    local charsLoaded, chars = pcall(require, "Backpack.data.chars")
    if charsLoaded and chars ~= nil then
        for charName, _ in pairs(chars) do
            table.insert(characters, charName)
        end
    end

    for _, charName in ipairs(characters) do
        local name, className, sectionIdName = string.match(charName, "(.+)~~~(.+)~~~(.+)")
        local charDataLoaded, charData = pcall(require, "Disk Tracker.data." .. charName)

        if charDataLoaded and charData ~= nil then
            local entryLabel = string.format("%s - %s - %s", name, className, sectionIdName)
            if imgui.TreeNode(entryLabel) then
                imgui.Columns(7, "DiskTable", true)
                imgui.Text("Tech") imgui.NextColumn()
                imgui.Text("Level") imgui.NextColumn()
                imgui.Text("Disk") imgui.NextColumn()
                imgui.Text("") imgui.NextColumn()
                imgui.Text("") imgui.NextColumn()
                imgui.Text("") imgui.NextColumn()
                imgui.Text("") imgui.NextColumn()
                imgui.Separator()

                for techName, learnedLevel in pairs(charData.techniques) do
                    local maxLevel = requirements.max_levels[className][techName]
                    if learnedLevel < maxLevel then
                        imgui.Text(techName)
                        imgui.NextColumn()
                        imgui.Text(tostring(learnedLevel))
                        imgui.NextColumn()

                        local availableDisks = Disks[charName] and Disks[charName][techName] or {}
                        table.sort(availableDisks, function(a, b) return a.level < b.level end)

                        local diskDisplayCount = 0
                        for _, disk in ipairs(availableDisks) do
                            if disk.level > learnedLevel and disk.level <= maxLevel and diskDisplayCount < 5 then
                                local mindReq = requirements.mind_reqs[techName][disk.level]
                                local color = 0xFFFFFFFF
                                if charData.mind >= mindReq then
                                    color = 0xFF00FF00 -- Green
                                end
                                local diskText = string.format("%d - %s", disk.level, disk.loc)
                                lib_helpers.TextC(false, color, diskText)
                                imgui.NextColumn()
                                diskDisplayCount = diskDisplayCount + 1
                            end
                        end
                        -- Fill remaining columns
                        for i = diskDisplayCount, 4 do
                            imgui.NextColumn()
                        end
                    end
                end
                imgui.Columns(1)
                imgui.TreePop()
            end
        end
    end
end

local function present()
    TrackCharacterData()
    -- If the addon has never been used, open the config window
    -- and disable the config window setting
    if options.configurationEnableWindow then
        ConfigurationWindow.open = true
        options.configurationEnableWindow = false
    end
    ConfigurationWindow.Update()

    if ConfigurationWindow.changed then
        ConfigurationWindow.changed = false
        SaveOptions(options)
    end

    if (options.EnableWindow == true)
            and (options.HideWhenMenu == false or lib_menu.IsMenuOpen() == true)
            and (options.HideWhenSymbolChat == false or lib_menu.IsSymbolChatOpen() == false)
            and (options.HideWhenMenuUnavailable == false or lib_menu.IsMenuUnavailable() == false) then
        local windowName = "Disk Tracker"

        if options.TransparentWindow == true then
            imgui.PushStyleColor("WindowBg", 0.0, 0.0, 0.0, 0.0)
        end

        imgui.SetNextWindowSizeConstraints(0, 0, options.W, options.H)

        if imgui.Begin(windowName,
            nil,
            {
                options.NoTitleBar,
                options.NoResize,
                options.NoMove,
                "AlwaysAutoResize",
            }) then
            PresentDiskTracker()

            lib_helpers.WindowPositionAndSize(windowName,
                options.X,
                options.Y,
                options.W,
                options.H,
                options.Anchor,
                "AlwaysAutoResize",
                options.changed)
        end
        imgui.End()

        if options.TransparentWindow == true then
            imgui.PopStyleColor()
        end

        options.changed = false
    end
end

local function init()
    ConfigurationWindow = cfg.ConfigurationWindow(options)
    Frame = 0

    local function mainMenuButtonHandler()
        ConfigurationWindow.open = not ConfigurationWindow.open
    end

    core_mainmenu.add_button("Disk Tracker", mainMenuButtonHandler)

    return
    {
        name = "Disk Tracker",
        version = "1.0.0",
        author = "Jules",
        description = "Tracks learned techniques and available technique disks.",
        present = present,
    }
end

return
{
    __addon =
    {
        init = init
    }
}
