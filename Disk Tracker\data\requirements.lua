local requirements = {}

requirements.max_levels = {
    -- <PERSON>
    HUmar = {
        <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
        <PERSON><PERSON> = 15, <PERSON> = 5, <PERSON><PERSON><PERSON> = 0,
        <PERSON><PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
        <PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON><PERSON> = 1,
    },
    HUnewearl = {
        Foie = 20, <PERSON><PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
        <PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
        <PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
        <PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
        <PERSON><PERSON> = 20, <PERSON> = 7, <PERSON>erser = 1,
        <PERSON>ft<PERSON> = 20, <PERSON><PERSON> = 20,
        <PERSON><PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
        <PERSON><PERSON><PERSON> = 1,
    },
    -- Rangers
    RAmar = {
        Foie = 15, <PERSON><PERSON>oi<PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON><PERSON> = 15, <PERSON><PERSON><PERSON> = 15,
        <PERSON><PERSON> = 0, <PERSON><PERSON> = 0,
        <PERSON><PERSON> = 15, <PERSON> = 5, <PERSON><PERSON><PERSON> = 0,
        <PERSON>ft<PERSON> = 15, <PERSON><PERSON> = 15,
        <PERSON><PERSON> = 0, <PERSON><PERSON><PERSON> = 0,
        <PERSON><PERSON><PERSON> = 1,
    },
    <PERSON><PERSON><PERSON> = {
        <PERSON>oi<PERSON> = 20, <PERSON><PERSON><PERSON><PERSON> = 20, <PERSON><PERSON>ie = 20,
        <PERSON>a = 20, G<PERSON>rta = 20, Ra<PERSON>ta = 20,
        Zonde = 20, <PERSON><PERSON>ond<PERSON> = 20, Ra<PERSON>de = 20,
        <PERSON>s = 0, <PERSON>id = 0,
        <PERSON>a = 20, <PERSON> = 7, <PERSON>erser = 1,
        Shifta = 20, <PERSON>band = 20,
        Je<PERSON> = 20, <PERSON><PERSON><PERSON> = 20,
        Ryuker = 1,
    },
    -- Forces
    <PERSON><PERSON>mar = {
        <PERSON>oie = 30, Gifoie = 30, <PERSON><PERSON>ie = 30,
        <PERSON>a = 30, Gibarta = 30, Rabarta = 30,
        Zonde = 30, Gizonde = 30, Razonde = 30,
        Grants = 30, Megid = 30,
        Resta = 30, Anti = 7, Reverser = 1,
        Shifta = 30, Deband = 30,
        Jellen = 30, Zalure = 30,
        Ryuker = 1,
    },
    FOmarl = {
        Foie = 30, Gifoie = 30, Rafoie = 30,
        Barta = 30, Gibarta = 30, Rabarta = 30,
        Zonde = 30, Gizonde = 30, Razonde = 30,
        Grants = 30, Megid = 30,
        Resta = 30, Anti = 7, Reverser = 1,
        Shifta = 30, Deband = 30,
        Jellen = 30, Zalure = 30,
        Ryuker = 1,
    },
    FOnewm = {
        Foie = 30, Gifoie = 30, Rafoie = 30,
        Barta = 30, Gibarta = 30, Rabarta = 30,
        Zonde = 30, Gizonde = 30, Razonde = 30,
        Grants = 30, Megid = 30,
        Resta = 30, Anti = 7, Reverser = 1,
        Shifta = 30, Deband = 30,
        Jellen = 30, Zalure = 30,
        Ryuker = 1,
    },
    FOnewearl = {
        Foie = 30, Gifoie = 30, Rafoie = 30,
        Barta = 30, Gibarta = 30, Rabarta = 30,
        Zonde = 30, Gizonde = 30, Razonde = 30,
        Grants = 30, Megid = 30,
        Resta = 30, Anti = 7, Reverser = 1,
        Shifta = 30, Deband = 30,
        Jellen = 30, Zalure = 30,
        Ryuker = 1,
    },
}

requirements.mind_reqs = {
    Foie = { 40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620 },
    Gifoie = { 100, 125, 150, 175, 200, 225, 250, 275, 300, 325, 350, 375, 400, 425, 450, 475, 500, 525, 550, 575, 600, 625, 650, 675, 700, 725, 750, 775, 800, 825 },
    Rafoie = { 133, 161, 189, 217, 245, 273, 301, 329, 357, 385, 413, 441, 469, 497, 525, 553, 581, 609, 637, 665, 693, 721, 749, 777, 805, 833, 861, 889, 917, 945 },
    Barta = { 44, 68, 92, 116, 140, 164, 188, 212, 236, 260, 284, 308, 332, 356, 380, 404, 428, 452, 476, 500, 524, 548, 572, 596, 620, 644, 668, 692, 716, 740 },
    Gibarta = { 110, 137, 164, 191, 218, 245, 272, 299, 326, 353, 380, 407, 434, 461, 488, 515, 542, 569, 596, 623, 650, 677, 704, 731, 758, 785, 812, 839, 866, 893 },
    Rabarta = { 143, 173, 203, 233, 263, 293, 323, 353, 383, 413, 443, 473, 503, 533, 563, 593, 623, 653, 683, 713, 743, 773, 803, 833, 863, 893, 923, 953, 983, 1013 },
    Zonde = { 50, 76, 102, 128, 154, 180, 206, 232, 258, 284, 310, 336, 362, 388, 414, 440, 466, 492, 518, 544, 570, 596, 622, 648, 674, 700, 726, 752, 778, 804 },
    Gizonde = { 120, 148, 176, 204, 232, 260, 288, 316, 344, 372, 400, 428, 456, 484, 512, 540, 568, 596, 624, 652, 680, 708, 736, 764, 792, 820, 848, 876, 904, 932 },
    Razonde = { 153, 185, 217, 249, 281, 313, 345, 377, 409, 441, 473, 505, 537, 569, 601, 633, 665, 697, 729, 761, 793, 825, 857, 889, 921, 953, 985, 1017, 1049, 1081 },
    Grants = { 200, 232, 264, 296, 328, 360, 392, 424, 456, 488, 520, 552, 584, 616, 648, 680, 712, 744, 776, 808, 840, 872, 904, 936, 968, 1000, 1032, 1064, 1096, 1128 },
    Megid = { 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620, 640, 660, 680, 700, 720, 740, 760, 780, 800 },
    Resta = { 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300, 310, 320, 330, 340 },
    Anti = { 80, 100, 120, 140, 160, 180, 200 },
    Shifta = { 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620, 640 },
    Deband = { 60, 80, 100, 120, 140, 160, 180, 200, 220, 240, 260, 280, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 540, 560, 580, 600, 620, 640 },
    Jellen = { 70, 90, 110, 130, 150, 170, 190, 210, 230, 250, 270, 290, 310, 330, 350, 370, 390, 410, 430, 450, 470, 490, 510, 530, 550, 570, 590, 610, 630, 650 },
    Zalure = { 70, 90, 110, 130, 150, 170, 190, 210, 230, 250, 270, 290, 310, 330, 350, 370, 390, 410, 430, 450, 470, 490, 510, 530, 550, 570, 590, 610, 630, 650 },
    Reverser = { 0 },
    Ryuker = { 0 },
}

return requirements
